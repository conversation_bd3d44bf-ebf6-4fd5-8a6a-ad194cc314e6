<template>
  <div class="app-container">
    <el-form :model="vimQueryParams" ref="vimQueryRef" :inline="true" v-show="vimShowSearch"
      label-width="80px" class="compact-form">
      <el-row :gutter="15">
        <!-- 第一行 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="用户ID" prop="id">
            <el-input v-model="vimQueryParams.id" placeholder="请输入用户ID" clearable class="compact-input"
              @keyup.enter="handleVimQuery" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="昵称" prop="nickname">
            <el-input v-model="vimQueryParams.nickname" placeholder="请输入用户昵称" clearable
              class="compact-input" @keyup.enter="handleVimQuery" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="vimQueryParams.phone" placeholder="请输入手机号" clearable
              class="compact-input" @keyup.enter="handleVimQuery" />
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="6" :md="8" :lg="6" class="action-col">
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleVimQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetVimQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleVimAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="vimSingle"
          @click="handleVimUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="vimMultiple"
          @click="handleVimDelete">删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="vimShowSearch" @queryTable="getVimList"
        :columns="vimColumns"></right-toolbar>
    </el-row>

    <el-table v-loading="vimLoading" :data="vimUserList" @selection-change="handleVimSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="用户ID" align="center" key="id" prop="id" v-if="vimColumns[0].visible" width="80" />
      <el-table-column label="用户名" align="center" key="username" prop="username"
        v-if="vimColumns[1].visible" :show-overflow-tooltip="true" />
      <el-table-column label="昵称" align="center" key="nickname" prop="nickname" v-if="vimColumns[2].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="手机号" align="center" key="phone" prop="phone" v-if="vimColumns[3].visible"
        width="120" />
      <el-table-column label="电能余额" align="center" key="coin" prop="coin" v-if="vimColumns[4].visible"
        width="100">
        <template #default="scope">
          <span>{{ scope.row.coin || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="钥匙余额" align="center" key="key" prop="key" v-if="vimColumns[5].visible"
        width="100">
        <template #default="scope">
          <span>{{ scope.row.key || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" align="center" prop="vimUsercreateTime" v-if="vimColumns[6].visible"
        width="160" sortable>
        <template #default="scope">
          <span>{{ formatCreateTime(scope.row.vimUsercreateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleVimUpdate(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleVimDelete(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="重置密码" placement="top">
            <el-button link type="warning" icon="Key" @click="handleVimResetPassword(scope.row)"></el-button>
          </el-tooltip>
          <el-tooltip content="查看详情" placement="top">
            <el-button link type="primary" icon="View" @click="handleVimView(scope.row)"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="vimTotal > 0" :total="vimTotal" v-model:page="vimQueryParams.pageNum"
      v-model:limit="vimQueryParams.pageSize" @pagination="getVimList" />

    <!-- 添加或修改盲盒用户对话框 -->
    <el-dialog :title="vimTitle" v-model="vimOpen" width="600px" append-to-body>
      <el-form :model="vimForm" :rules="vimRules" ref="vimUserRef" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户名" prop="username">
              <el-input v-model="vimForm.username" placeholder="请输入用户名" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="vimForm.nickname" placeholder="请输入昵称" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="vimForm.phone" placeholder="请输入手机号" maxlength="11" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="vimForm.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitVimForm">确 定</el-button>
          <el-button @click="cancelVim">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog title="用户详情" v-model="vimViewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户ID">{{ vimViewData.uid }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ vimViewData.username }}</el-descriptions-item>
        <el-descriptions-item label="昵称">{{ vimViewData.nickname }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ vimViewData.email }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ vimViewData.phone }}</el-descriptions-item>
        <el-descriptions-item label="余额">{{ vimViewData.balance || 0 }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="vimViewData.status === '0' ? 'success' : 'danger'">
            {{ vimViewData.status === '0' ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(vimViewData.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ vimViewData.remark }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="vimViewOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, toRefs, getCurrentInstance } from 'vue'
import { listVimUsers, getUserInfo, addVimUsers, updateVimUsers, delVimUsers, resetVimUserPassword } from "@/api/VimUserSys/VimUsers"

const { proxy } = getCurrentInstance()

// 响应式数据
const vimUserList = ref([])
const vimOpen = ref(false)
const vimLoading = ref(true)
const vimShowSearch = ref(true)
const vimIds = ref([])
const vimSingle = ref(true)
const vimMultiple = ref(true)
const vimTotal = ref(0)
const vimTitle = ref("")
const vimViewOpen = ref(false)
const vimViewData = ref({})

// 列显隐信息
const vimColumns = ref([
  { key: 0, label: `用户ID`, visible: true },
  { key: 1, label: `用户名`, visible: true },
  { key: 2, label: `昵称`, visible: true },
  { key: 3, label: `手机号`, visible: true },
  { key: 4, label: `电能余额`, visible: true },
  { key: 5, label: `钥匙余额`, visible: true },
  { key: 6, label: `注册时间`, visible: true }
])

const vimData = reactive({
  vimForm: {},
  vimQueryParams: {
    pageNum: 1,
    pageSize: 10,
    id: undefined,
    nickname: undefined,
    phone: undefined
  },
  vimRules: {
    username: [{ required: true, message: "用户名不能为空", trigger: "blur" }, { min: 2, max: 50, message: "用户名长度必须介于 2 和 50 之间", trigger: "blur" }],
    nickname: [{ required: true, message: "昵称不能为空", trigger: "blur" }],
    email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }],
    phone: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }]
  }
})

const { vimQueryParams, vimForm, vimRules } = toRefs(vimData)

// 格式化创建时间
function formatCreateTime(timestamp) {
  if (!timestamp) return '-'
  // 如果是秒级时间戳，转换为毫秒级
  const time = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp
  return proxy.parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

// 查询盲盒用户列表
function getVimList() {
  vimLoading.value = true
  listVimUsers(vimQueryParams.value).then(res => {
    vimLoading.value = false
    vimUserList.value = res.rows
    vimTotal.value = res.total
  }).catch(() => {
    vimLoading.value = false
  })
}

// 盲盒用户搜索按钮操作
function handleVimQuery() {
  vimQueryParams.value.pageNum = 1
  getVimList()
}

// 盲盒用户重置按钮操作
function resetVimQuery() {
  proxy.resetForm("vimQueryRef")
  handleVimQuery()
}

// 盲盒用户选择条数
function handleVimSelectionChange(selection) {
  vimIds.value = selection.map(item => item.uid)
  vimSingle.value = selection.length != 1
  vimMultiple.value = !selection.length
}

// 重置盲盒用户操作表单
function resetVim() {
  vimForm.value = {
    uid: undefined,
    username: undefined,
    nickname: undefined,
    email: undefined,
    phone: undefined,
    balance: 0,
    status: "1",
    remark: undefined
  }
  proxy.resetForm("vimUserRef")
}

// 取消盲盒用户按钮
function cancelVim() {
  vimOpen.value = false
  resetVim()
}

// 新增盲盒用户按钮操作
function handleVimAdd() {
  resetVim()
  vimOpen.value = true
  vimTitle.value = "添加盲盒用户"
}

// 修改盲盒用户按钮操作
function handleVimUpdate(row) {
  resetVim()
  const uid = row.uid || vimIds.value
  getUserInfo(uid).then(response => {
    vimForm.value = response.data
    vimOpen.value = true
    vimTitle.value = "修改盲盒用户"
  })
}

// 提交盲盒用户按钮
function submitVimForm() {
  proxy.$refs["vimUserRef"].validate(valid => {
    if (valid) {
      if (vimForm.value.uid != undefined) {
        updateVimUsers(vimForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          vimOpen.value = false
          getVimList()
        })
      } else {
        addVimUsers(vimForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          vimOpen.value = false
          getVimList()
        })
      }
    }
  })
}

// 删除盲盒用户按钮操作
function handleVimDelete(row) {
  const uids = row.uid || vimIds.value
  proxy.$modal.confirm('是否确认删除用户ID为"' + uids + '"的数据项？').then(function () {
    return delVimUsers(uids)
  }).then(() => {
    getVimList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

// 查看盲盒用户详情
function handleVimView(row) {
  getUserInfo(row.uid).then(response => {
    vimViewData.value = response.data
    vimViewOpen.value = true
  })
}

// 重置盲盒用户密码
function handleVimResetPassword(row) {
  const userId = row.uid;
  const username = row.username || '该用户';

  proxy.$modal.confirm(`确认要重置用户"${username}"的密码吗？重置后密码将变为默认密码"123456"。`, "重置密码确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(function() {
    return resetVimUserPassword(userId);
  }).then(() => {
    proxy.$modal.msgSuccess("密码重置成功，新密码为：123456");
  }).catch((error) => {
    if (error !== 'cancel') {
      proxy.$modal.msgError("密码重置失败");
    }
  });
}

// 初始化
getVimList()

// 暴露方法给父组件
defineExpose({
  getVimList
})
</script>

<style scoped>
.compact-form :deep(.el-form-item) {
  margin-bottom: 12px;
}

.compact-input :deep(.el-input__inner),
.compact-select :deep(.el-select__wrapper) {
  height: 32px;
  line-height: 32px;
}

.compact-input :deep(.el-input),
.compact-select :deep(.el-select) {
  width: 100%;
}

.action-col :deep(.el-form-item__content) {
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .action-col :deep(.el-form-item__content) {
    justify-content: flex-start;
  }
}
</style>