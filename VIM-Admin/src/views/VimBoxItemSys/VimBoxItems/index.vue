<template>
  <!-- 整个商品管理界面的容器 -->
  <div class="app-container">
    <!-- 搜索表单，用于输入查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮行 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <!-- 新增按钮，需要有相应权限 -->
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['commoditySys:commoditys:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <!-- 修改按钮，需要有相应权限，当只选择一个商品时可用 -->
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['commoditySys:commoditys:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <!-- 删除按钮，需要有相应权限，当选择多个商品时可用 -->
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['commoditySys:commoditys:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <!-- 导出按钮，需要有相应权限 -->
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['commoditySys:commoditys:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="3">
        <!-- 总权重显示区域 -->
        <div class="total-weight-display">
          <el-tag
            :type="totalWeightTagType"
            size="large"
            effect="dark"
            style="font-size: 14px; padding: 8px 16px; margin-left: 10px;"
          >
            总权重: {{ totalWeight }}
          </el-tag>
        </div>
      </el-col>
      <el-col :span="3">
        <!-- 盲盒价格显示区域 -->
        <div class="box-price-display">
          <el-tag
            type="warning"
            size="large"
            effect="dark"
            style="font-size: 14px; padding: 8px 16px; margin-left: 10px;"
          >
            盲盒价格: ¥{{ formatBoxPrice(boxPrice) }}
          </el-tag>
        </div>
      </el-col>
      <el-col :span="3">
        <!-- 返奖率显示区域 -->
        <div class="return-rate-display">
          <el-tag
            :type="returnRateTagType"
            size="large"
            effect="dark"
            style="font-size: 14px; padding: 8px 16px; margin-left: 10px;"
            v-loading="returnRateLoading"
          >
            返奖率: {{ returnRateDisplay }}
          </el-tag>
        </div>
      </el-col>
      <!-- 右侧工具栏，用于控制搜索框的显示和触发列表查询 -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="commoditysList" @selection-change="handleSelectionChange">
      <el-table-column label="物品id" align="center" prop="vimItem.id" width="80"/>
      <el-table-column label="物品名称" align="center" prop="vimItem.name" />
      <el-table-column label="权重" align="center" prop="vimBoxItem.probability" width="60"/>
      <el-table-column label="成本价格" align="center" width="100" sortable>
        <template #default="scope">
          <span class="price-text">¥{{ formatPrice(scope.row.vimItem.priceCost) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="回收价格" align="center" width="100" sortable>
        <template #default="scope">
          <span class="price-text">¥{{ formatPrice(scope.row.vimItem.priceRecycle) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="概率等级" align="center" prop="vimBoxItem.level" width="100" />
      <el-table-column label="物品图片" align="center" width="100">
        <template #default="scope">
          <image-preview
              :src="scope.row.vimItem.image"
              :width="50"
              :height="50"
              class="preview-image"
          />
        </template>
      </el-table-column>
      <el-table-column label="修改时间" align="center">
        <template #default="scope">
          {{ formatTime(scope.row.vimBoxItem?.vimBoxItemupdateTime) }}
        </template>
      </el-table-column>
      <!-- 调整后的操作列 -->
      <el-table-column
          label="操作"
          align="center"
          width="160"
          class-name="compact-operation"
      >
        <template #default="scope">
          <div style="display: flex; justify-content: center; gap: 8px">
            <el-button
                link
                type="primary"
                icon="Edit"
                style="padding: 0"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['commoditySys:commoditys:edit']"
            >修改</el-button>
            <el-button
                link
                type="primary"
                icon="Delete"
                style="padding: 0"
                @click="handleDelete(scope.row)"
                v-hasPermi="['commoditySys:commoditys:remove']"
            >删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>



    <!-- 分页组件，当总记录数大于0时显示 -->
    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <template>
      <!-- 添加或修改商品管理对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body> <!-- 修改宽度为800px -->
        <!-- 商品表单 -->
        <el-form ref="commoditysRef" :model="form" :rules="rules" label-width="120px"> <!-- 增加标签宽度 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="物品名称：" prop="idItem">
                <el-autocomplete
                    v-model="searchName"
                :fetch-suggestions="querySearchAsync"
                placeholder="请输入物品名称"
                @select="handleSelect"
                style="width: 280px"
                ></el-autocomplete>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="概率权重：" prop="probability">
                <el-input
                    v-model.number="form.probability"
                    placeholder="请输入概率权重"
                    type="number"
                    style="width: 280px"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="概率等级：" prop="level">
                <el-select
                    v-model="form.level"
                    placeholder="请选择等级"
                    style="width: 280px"
                >
                  <el-option
                      v-for="item in levelOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer" style="text-align: center"> <!-- 居中按钮 -->
            <el-button type="primary" @click="submitForm" style="width: 100px">确 定</el-button>
            <el-button @click="cancel" style="width: 100px; margin-left: 30px">取 消</el-button>
          </div>
        </template>
      </el-dialog>
    </template>
  </div>
</template>

<script setup name="Commoditys">
import { listCommoditys, getCommoditys, delCommoditys, addCommoditys, updateCommoditys } from "@/api/commoditySys/commoditys";
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { onMounted } from 'vue'
import {
  addVimBoxItems,
  delVimBoxItems,
  selectVimItemAndVimBoxItemByBoxId,
  updateVimBoxItems
} from "@/api/VimBoxItemSys/VimBoxItems.js";
import { getVimBoxReturnRate, getVimBoxs } from "@/api/vimBoxSys/vimBoxs.js";
import { debounce } from 'lodash-es';

const route = useRoute()
// 新增响应式数据
const suggestions = ref([]); // 临时存储输入框显示的名称
const loadingSuggestions = ref(false);
const searchName = ref('');
const handleSelect = (item) => {
  form.value.idItem = item.id; // 新字段名
  searchName.value = item.name;
};
// 模糊查询方法（带防抖）
const querySearchAsync = debounce(async (queryString, cb) => {
  if (!queryString) {
    cb([]);
    return;
  }

  try {
    loadingSuggestions.value = true;
    const response = await listCommoditys({
      name: queryString,
      pageNum: 1,
      pageSize: 10
    });

    // 转换数据结构：{ value: 显示文本, ...原始数据 }
    if (response.rows) {
      suggestions.value = response.rows.map(item => ({
        value: item.name,
        ...item
      }));
      cb(suggestions.value);
    } else {
      cb([]);
    }
  } catch (error) {
    ElMessage.error('查询失败');
    cb([]);
  } finally {
    loadingSuggestions.value = false;
  }
}, 300); // 防抖300ms


onMounted(() => {
  // 检查idBox是否存在
  if (!route.query.idBox) {
    ElMessage.error('缺少必要参数')
    router.back()
  }
})

const handleExceed = () => {
  ElMessage.warning('只能上传一张图片')
}

function formatTime(timestamp) {
  if (!timestamp) return '-';
  const date = new Date(timestamp * 1000); // 关键修改：秒级转毫秒级
  return date.toLocaleString();
}
// 获取当前组件实例
const { proxy } = getCurrentInstance();

// 商品列表数据
const commoditysList = ref([]);
// 对话框显示状态
const open = ref(false);
// 加载状态
const loading = ref(true);
// 搜索框显示状态
const showSearch = ref(true);
// 选中的商品id数组
const ids = ref([]);
// 是否只选择了一个商品
const single = ref(true);
// 是否没有选择商品
const multiple = ref(true);
// 总记录数
const total = ref(0);
// 对话框标题
const title = ref("");
// 返奖率相关数据
const returnRate = ref(0);
const returnRateLoading = ref(false);
// 盲盒价格数据
const boxPrice = ref(0);
// 新增标签选项数据
const tagOptions = [
  { label: '崭新出厂', value: '崭新出厂' },
  { label: '略有磨损', value: '略有磨损' },
  { label: '久经沙场', value: '久经沙场' },
  { label: '破损不堪', value: '破损不堪' },
  { label: '战痕累累', value: '战痕累累' },
  // 可以根据实际情况添加更多标签选项
];
const levelOptions = [
  { label: '等级1', value: 1 },
  { label: '等级2', value: 2 },
  { label: '等级3', value: 3 },
  { label: '等级4', value: 4 },
  { label: '等级5', value: 5 }
];
// 响应式数据对象
const data = reactive({
  // 商品表单数据
  form: {
    // sale:null,
  },
  // 查询参数
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    idItem: null,
    idBox:null,
    probability:null,
    level:null,
  },
  // 表单验证规则
  rules: {
    name: [
      { required: true, message: "物品名称不能为空", trigger: "blur" }
    ],
    probability: [  // 概率权重验证
      { required: true, message: "概率权重不能为空", trigger: "blur" },
      { type: 'number', message: "必须为数字" }
    ],
    level: [        // 概率等级验证
      { required: true, message: "请选择概率等级", trigger: "change" }
    ],
  }
});

// 将响应式数据对象的属性转换为响应式引用
const { queryParams, form, rules } = toRefs(data);

// 计算总权重
const totalWeight = computed(() => {
  if (!commoditysList.value || commoditysList.value.length === 0) {
    return 0;
  }

  return commoditysList.value.reduce((total, item) => {
    const probability = item.vimBoxItem?.probability || 0;
    return total + Number(probability);
  }, 0);
});

// 根据总权重值决定标签颜色
const totalWeightTagType = computed(() => {
  const weight = totalWeight.value;
  if (weight === 0) {
    return 'info';     // 灰色 - 没有配置
  } else if (weight === 1000) {
    return 'success';  // 绿色 - 标准权重
  } else if (weight < 1000) {
    return 'warning';  // 橙色 - 权重不足
  } else {
    return 'danger';   // 红色 - 权重超标
  }
});



// 返奖率显示格式
const returnRateDisplay = computed(() => {
  if (returnRateLoading.value) {
    return '计算中...';
  }
  if (returnRate.value === 0) {
    return '0%';
  }
  return `${returnRate.value}%`;
});

// 根据返奖率值决定标签颜色
const returnRateTagType = computed(() => {
  const rate = returnRate.value;
  if (rate === 0) {
    return 'info';     // 灰色 - 没有数据
  } else if (rate < 80) {
    return 'danger';   // 红色 - 返奖率过低
  } else if (rate >= 80 && rate < 100) {
    return 'warning';  // 橙色 - 返奖率正常
  } else {
    return 'success';  // 绿色 - 返奖率良好
  }
});

// 格式化盲盒价格显示
const formatBoxPrice = (price) => {
  if (!price || price === 0) {
    return '0.00';
  }
  return Number(price).toFixed(2);
};


/** 获取盲盒价格 */
async function getBoxPrice() {
  if (!queryParams.value.idBox) {
    return;
  }

  try {
    const response = await getVimBoxs(queryParams.value.idBox);
    if (response && response.data && response.data.price !== undefined) {
      boxPrice.value = Number(response.data.price) || 0;
    } else {
      boxPrice.value = 0;
    }
  } catch (error) {
    boxPrice.value = 0;
  }
}

/** 获取盲盒返奖率 */
async function getReturnRate() {
  if (!queryParams.value.idBox) {
    return;
  }

  try {
    returnRateLoading.value = true;
    const response = await getVimBoxReturnRate(queryParams.value.idBox);
    if (response && response.data !== undefined) {
      returnRate.value = Number(response.data) || 0;
    } else {
      returnRate.value = 0;
    }
  } catch (error) {
    returnRate.value = 0;
  } finally {
    returnRateLoading.value = false;
  }
}

/** 查询商品列表 */
async function getList() {
  loading.value = true;
  try {
    // 检查idBox
    if (!queryParams.value.idBox) {
      ElMessage.error('缺少必要参数: idBox');
      return;
    }

    // 构造请求参数
    const params = {
      idBox: queryParams.value.idBox,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize
    };

    // 发起请求
    const response = await selectVimItemAndVimBoxItemByBoxId(params);
    if (Array.isArray(response)) {
      commoditysList.value = response;
      total.value = response.total;
    } else if (response?.rows) {
      commoditysList.value = response.rows;
      total.value = response.total;
    } else {
      ElMessage.error('数据格式错误');
    }

    // 获取商品列表成功后，同时获取盲盒价格和返奖率
    await Promise.all([getBoxPrice(), getReturnRate()]);
  } catch (error) {
    ElMessage.error('加载失败');
  } finally {
    loading.value = false;
  }
}

// 取消按钮操作
function cancel() {
  // 关闭对话框
  open.value = false;
  // 重置表单
  reset();
}

// 表单重置操作
function reset() {
  // 重置表单数据
  form.value = {
    idItem: null,
    idBox:null,
    probability:null,
    level:null,
  };
  // 调用Element UI的表单重置方法
  proxy.resetForm("commoditysRef");
}

// 多选框选中数据变化时的操作
function handleSelectionChange(selection) {
  // 更新选中的商品id数组
  ids.value = selection.map(item => item.id);
  // 判断是否只选择了一个商品
  single.value = selection.length != 1;
  // 判断是否没有选择商品
  multiple.value = !selection.length;
}

/**
 * 新增按钮操作
 */
function handleAdd() {
  // 重置表单
  reset();
  form.value = {
    ...form.value,
    idBox:queryParams.value.idBox,
  };
  // 打开对话框
  open.value = true;
  // 设置对话框标题
  title.value = "添加商品管理";
}

/**
 * 修改按钮操作
 */
function handleUpdate(row) {
  // 从行数据中直接获取必要字段（无需调用接口）
  form.value = {
    id:row.vimBoxItem?.id,
    idItem: row.vimItem?.idItem,
    idBox: row.vimBoxItem?.idBox,    // 盲盒ID
    probability: row.vimBoxItem?.probability, // 概率权重
    level: row.vimBoxItem?.level     // 概率等级
  };
  // 设置输入框显示的名称
  searchName.value = row.vimItem?.name;
  // 打开弹窗
  open.value = true;
  title.value = "修改商品管理";
}


/**
 * 提交按钮操作
 */
function submitForm() {
  // 验证表单
  proxy.$refs["commoditysRef"].validate(valid => {
    if (valid) {
      const submitData = {
        ...form.value,
        idItem: form.value.idItem // 明确传递ID
      };
      if (submitData.id != null) {
        // 如果商品id存在，调用修改接口
        updateVimBoxItems(submitData).then(response => {
          // 显示修改成功提示
          proxy.$modal.msgSuccess("修改成功");
          // 关闭对话框
          open.value = false;
          // 重新获取商品列表
          getList();
        });
      } else {
        addVimBoxItems(submitData).then(response => {
          // 显示新增成功提示
          proxy.$modal.msgSuccess("新增成功");
          // 关闭对话框
          open.value = false;
          // 重新获取商品列表
          getList();
        });
      }
    }
  });
}

/**
 * 删除按钮操作
 */
function handleDelete(row) {
  // form.value = {
  //   id:row.vimBoxItem?.id,
  //   idItem: row.vimItem?.idItem,
  //   idBox: row.vimBoxItem?.idBox,    // 盲盒ID
  //   probability: row.vimBoxItem?.probability, // 概率权重
  //   level: row.vimBoxItem?.level     // 概率等级
  // };
  // 获取要删除的商品id
  const _ids = row.vimBoxItem?.id || ids.value;
  // 弹出确认框
  proxy.$modal.confirm('是否确认删除商品管理编号为"' + _ids + '"的数据项？').then(function() {
    // 确认后调用删除接口
    return delVimBoxItems(_ids);
  }).then(() => {
    // 删除成功后重新获取商品列表
    getList();
    // 显示删除成功提示
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}
// 监听路由参数变化
watch(
    () => route.query.idBox,
    (newIdBox) => {
      if (newIdBox) {
        queryParams.value.idBox = newIdBox;
        getList(); // 参数更新后重新获取数据
      }
    },
    { immediate: true } // 立即执行一次
);
/**
 * 导出按钮操作
 */
function handleExport() {
  // 调用下载方法导出商品列表
  proxy.download('commoditySys/commoditys/export', {
    ...queryParams.value
  }, `commoditys_${new Date().getTime()}.xlsx`)
}

/**
 * 格式化价格显示
 * @param {number|string} price 价格值
 * @returns {string} 格式化后的价格字符串
 */
function formatPrice(price) {
  if (price === null || price === undefined || price === '') {
    return '0.00';
  }

  const numPrice = Number(price);
  if (isNaN(numPrice)) {
    return '0.00';
  }

  return numPrice.toFixed(2);
}

// 初始化时获取商品列表
getList();
</script>
<!--修改删除和修改按键位置-->
<style scoped>
:deep(.compact-operation .cell) {
  padding: 0 5px !important;
}
:deep(.el-autocomplete-suggestion) {
  z-index: 9999 !important;
}
:deep(.preview-image img) {
  object-fit: contain !important;
  width: 100% !important;
  height: 100% !important;
}

/* 价格文本样式 */
.price-text {
  font-weight: 600;
  color: #409EFF;
  font-size: 14px;
}

/* 总权重显示区域样式 */
.total-weight-display,
.box-price-display,
.return-rate-display {
  display: flex;
  align-items: center;
  height: 32px; /* 与按钮高度保持一致 */
}

/* 标签样式增强 */
.total-weight-display .el-tag,
.return-rate-display .el-tag {
  font-weight: bold;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.total-weight-display .el-tag:hover,
.return-rate-display .el-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 返奖率加载状态样式 */
.return-rate-display .el-tag.is-loading {
  opacity: 0.8;
}
</style>