<template>
  <div class="app-container">
    <!-- 页面标题和统计信息 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span class="card-title">成本价异常商品管理</span>
            <el-button 
              style="float: right; padding: 3px 0" 
              type="text" 
              @click="loadStatistics"
              :loading="statisticsLoading"
            >
              刷新统计
            </el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">{{ statistics.totalAnomalyItems || 0 }}</div>
                <div class="statistic-label">异常商品总数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">¥{{ statistics.avgPriceDifference || '0.00' }}</div>
                <div class="statistic-label">平均价格差异</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">¥{{ statistics.maxPriceDifference || '0.00' }}</div>
                <div class="statistic-label">最大价格差异</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">{{ Object.keys(statistics.tagStatistics || {}).length }}</div>
                <div class="statistic-label">涉及商品类型</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索表单 -->
    <el-card class="box-card mb-4">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="商品名称" prop="itemName">
          <el-input
            v-model="queryParams.itemName"
            placeholder="请输入商品名称"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="盲盒名称" prop="boxName">
          <el-input
            v-model="queryParams.boxName"
            placeholder="请输入盲盒名称"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="商品标签" prop="itemTag">
          <el-input
            v-model="queryParams.itemTag"
            placeholder="请输入商品标签"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb-8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['priceAlert:simple:anomaly:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table 
      v-loading="loading" 
      :data="anomalyList" 
      @selection-change="handleSelectionChange"
      :default-sort="{prop: 'priceDifference', order: 'descending'}"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="商品信息" align="center" min-width="200">
        <template #default="scope">
          <div v-if="scope.row" class="item-info">
            <el-image
              :src="scope.row.itemImage"
              :preview-src-list="[scope.row.itemImage]"
              fit="cover"
              style="width: 40px; height: 40px; border-radius: 4px; margin-right: 10px;"
              :preview-teleported="true"
            >
              <template #error>
                <div class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </template>
            </el-image>
            <div class="item-details">
              <div class="item-name">{{ scope.row.itemName || '未知商品' }}</div>
              <div class="item-tag">{{ scope.row.itemTag || '未分类' }}</div>
            </div>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="价格信息" align="center" min-width="180">
        <template #default="scope">
          <div v-if="scope.row" class="price-info">
            <div class="price-row">
              <span class="price-label">成本价:</span>
              <span class="price-value cost-price">¥{{ scope.row.priceCost || '0.00' }}</span>
            </div>
            <div class="price-row">
              <span class="price-label">回收价:</span>
              <span class="price-value recycle-price">¥{{ scope.row.priceRecycle || '0.00' }}</span>
            </div>
            <div class="price-row">
              <span class="price-label">展示价:</span>
              <span class="price-value">¥{{ scope.row.priceShow || '0.00' }}</span>
            </div>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="价格差异" align="center" prop="priceDifference" sortable min-width="120">
        <template #default="scope">
          <div v-if="scope.row" class="difference-info">
            <div class="difference-amount">
              <el-tag type="danger" size="small">
                ¥{{ scope.row.priceDifference || '0.00' }}
              </el-tag>
            </div>
            <div class="difference-percentage">
              {{ scope.row.differencePercentage || '0.00' }}%
            </div>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="所属盲盒" align="center" min-width="200">
        <template #default="scope">
          <div v-if="scope.row">
            <div v-if="scope.row.boxNames" class="box-info">
              <!-- 盲盒数量统计 -->
              <div class="box-count">
                <el-tag type="info" size="small">
                  共{{ scope.row.boxCount || 0 }}个盲盒
                </el-tag>
              </div>
              <!-- 盲盒名称列表 -->
              <div class="box-names">
                <el-tooltip
                  :content="scope.row.boxNames"
                  placement="top"
                  :disabled="scope.row.boxNames.length <= 30"
                >
                  <span class="box-names-text">
                    {{ scope.row.boxNames.length > 30 ? scope.row.boxNames.substring(0, 30) + '...' : scope.row.boxNames }}
                  </span>
                </el-tooltip>
              </div>
              <!-- 盲盒类型（如果有多种类型） -->
              <div v-if="scope.row.boxTypes" class="box-types">
                <span class="box-types-label">类型：</span>
                <span class="box-types-text">{{ scope.row.boxTypes }}</span>
              </div>
              <!-- 盲盒价格范围 -->
              <div v-if="scope.row.boxPrices" class="box-prices">
                <span class="box-prices-label">价格：</span>
                <span class="box-prices-text">{{ scope.row.boxPrices }}</span>
              </div>
            </div>
            <span v-else class="text-muted">未关联盲盒</span>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="商品状态" align="center" width="100">
        <template #default="scope">
          <div v-if="scope.row">
            <el-tag :type="scope.row.itemSale === 1 ? 'success' : 'danger'" size="small">
              {{ scope.row.itemSale === 1 ? '上架' : '下架' }}
            </el-tag>
          </div>
          <div v-else class="loading-placeholder">-</div>
        </template>
      </el-table-column>
      <el-table-column label="库存" align="center" prop="itemStock" width="80" />
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @update:page="handlePageChange"
      @update:limit="handleSizeChange"
      @pagination="handlePagination"
    />
  </div>
</template>

<script>
import { getCostPriceAnomalyList, getCostPriceAnomalyStatistics } from '@/api/priceAlert/simple'

export default {
  name: 'CostPriceAnomaly',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 统计信息加载状态
      statisticsLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 异常商品表格数据
      anomalyList: [],
      // 统计信息
      statistics: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        itemName: null,
        boxName: null,
        itemTag: null
      }
    }
  },
  created() {
    console.log('组件创建，初始查询参数：', this.queryParams)
    this.getList()
    this.loadStatistics()
  },
  mounted() {
    console.log('组件挂载完成，当前查询参数：', this.queryParams)
    console.log('分页组件测试 - 可在控制台调用 window.testCostPriceAnomalyPagination() 进行测试')
  },
  watch: {
    // 监听分页参数变化
    'queryParams.pageNum': function(newVal, oldVal) {
      console.log('页码变化：', oldVal, '->', newVal)
    },
    'queryParams.pageSize': function(newVal, oldVal) {
      console.log('每页大小变化：', oldVal, '->', newVal)
    }
  },
  methods: {
    /** 查询异常商品列表 */
    getList() {
      this.loading = true
      console.log('🔍 开始获取成本价异常商品列表')
      console.log('查询参数：', JSON.parse(JSON.stringify(this.queryParams)))
      console.log('当前时间戳：', new Date().toISOString())

      getCostPriceAnomalyList(this.queryParams).then(response => {
        console.log('✅ API响应成功：', response)

        // 简化数据处理，按照VIM系统标准格式
        this.anomalyList = response.rows || []
        this.total = response.total || 0

        console.log(`📊 数据加载完成：共${this.total}条记录，当前页${this.anomalyList.length}条`)
        console.log('当前页码：', this.queryParams.pageNum)
        console.log('每页大小：', this.queryParams.pageSize)

        this.loading = false
      }).catch(error => {
        console.error('❌ 获取数据失败：', error)
        this.$modal.msgError('获取数据失败：' + (error.message || '未知错误'))
        this.anomalyList = []
        this.total = 0
        this.loading = false
      })
    },
    /** 加载统计信息 */
    loadStatistics() {
      this.statisticsLoading = true
      console.log('开始加载统计信息')

      getCostPriceAnomalyStatistics().then(response => {
        console.log('统计信息API响应：', response)

        if (response && typeof response === 'object') {
          this.statistics = response.data || response || {}
        } else {
          console.warn('统计信息响应格式异常：', response)
          this.statistics = {}
        }

        console.log('处理后的统计信息：', this.statistics)
        this.statisticsLoading = false
      }).catch(error => {
        console.error('加载统计信息失败：', error)
        this.$modal.msgError('加载统计信息失败：' + (error.message || '未知错误'))
        this.statistics = {}
        this.statisticsLoading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      console.log('执行搜索，重置页码为1')
      this.queryParams.pageNum = 1
      console.log('搜索参数：', this.queryParams)
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      console.log('重置搜索条件')
      this.resetForm('queryForm')
      this.queryParams.pageNum = 1
      this.queryParams.itemName = null
      this.queryParams.boxName = null
      this.queryParams.itemTag = null
      console.log('重置后的参数：', this.queryParams)
      this.getList()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.itemId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 页码变化处理 */
    handlePageChange(page) {
      console.log('🔄 页码变化：', this.queryParams.pageNum, '->', page)
      this.queryParams.pageNum = Number(page)
      this.getList()
    },
    /** 每页大小变化处理 */
    handleSizeChange(size) {
      console.log('🔄 每页大小变化：', this.queryParams.pageSize, '->', size)
      this.queryParams.pageNum = 1 // 重置到第一页
      this.queryParams.pageSize = Number(size)
      this.getList()
    },
    /** 分页事件处理（备用） */
    handlePagination({ page, limit }) {
      console.group('🔄 分页事件处理（备用）')
      console.log('分页事件参数：', { page, limit })
      console.log('当前查询参数（更新前）：', JSON.parse(JSON.stringify(this.queryParams)))

      // 确保参数更新
      this.queryParams.pageNum = Number(page)
      this.queryParams.pageSize = Number(limit)

      console.log('更新后的查询参数：', JSON.parse(JSON.stringify(this.queryParams)))
      this.getList()

      console.groupEnd()
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('priceAlert/simple/cost-price-anomaly/export', {
        ...this.queryParams
      }, `成本价异常商品_${new Date().getTime()}.xlsx`)
    },
    /** 测试分页功能 */
    testPagination() {
      if (process.env.NODE_ENV === 'development') {
        console.group('🧪 分页功能测试')
        console.log('当前页码：', this.queryParams.pageNum)
        console.log('每页大小：', this.queryParams.pageSize)
        console.log('总记录数：', this.total)
        console.log('当前数据条数：', this.anomalyList.length)
        console.log('分页组件是否显示：', this.total > 0)

        // 模拟分页切换
        const testPage = this.queryParams.pageNum === 1 ? 2 : 1
        console.log(`模拟切换到第${testPage}页`)

        // 使用分页事件处理方法进行测试
        this.handlePagination({
          page: testPage,
          limit: this.queryParams.pageSize
        })

        console.groupEnd()
      }
    },
    /** 手动测试分页参数更新 */
    testPageParamUpdate() {
      if (process.env.NODE_ENV === 'development') {
        console.group('🔧 手动测试分页参数更新')

        const originalPage = this.queryParams.pageNum
        const testPage = originalPage === 1 ? 2 : 1

        console.log('原始页码：', originalPage)
        console.log('测试页码：', testPage)

        // 直接修改参数
        this.queryParams.pageNum = testPage
        console.log('修改后的查询参数：', this.queryParams)

        // 延迟调用API，观察参数是否正确传递
        setTimeout(() => {
          console.log('延迟后的查询参数：', this.queryParams)
          this.getList()
        }, 100)

        console.groupEnd()
      }
    },
    /** 检查分页组件状态 */
    checkPaginationStatus() {
      if (process.env.NODE_ENV === 'development') {
        console.group('📊 分页组件状态检查')
        console.log('Vue组件实例：', this)
        console.log('queryParams对象：', this.queryParams)
        console.log('queryParams是否为响应式：', this.$data.queryParams === this.queryParams)
        console.log('total值：', this.total)
        console.log('anomalyList长度：', this.anomalyList.length)
        console.log('loading状态：', this.loading)

        // 检查分页组件是否正确渲染
        const paginationEl = this.$el.querySelector('.pagination-container')
        console.log('分页组件DOM元素：', paginationEl)

        if (paginationEl) {
          const currentPageEl = paginationEl.querySelector('.el-pager .is-active')
          console.log('当前激活页码元素：', currentPageEl)
          console.log('当前激活页码文本：', currentPageEl ? currentPageEl.textContent : '未找到')
        }

        console.groupEnd()
      }
    },
    /** 模拟点击分页按钮 */
    simulatePageClick(targetPage) {
      if (process.env.NODE_ENV === 'development') {
        console.group(`🖱️ 模拟点击第${targetPage}页`)

        // 查找分页按钮
        const paginationEl = this.$el.querySelector('.pagination-container')
        if (paginationEl) {
          const pageButtons = paginationEl.querySelectorAll('.el-pager .number')
          const targetButton = Array.from(pageButtons).find(btn =>
            btn.textContent.trim() === targetPage.toString()
          )

          if (targetButton) {
            console.log('找到目标按钮：', targetButton)
            console.log('点击前的页码：', this.queryParams.pageNum)

            // 模拟点击事件
            targetButton.click()

            // 延迟检查结果
            setTimeout(() => {
              console.log('点击后的页码：', this.queryParams.pageNum)
              console.log('是否成功切换：', this.queryParams.pageNum === targetPage)
            }, 100)
          } else {
            console.warn('未找到目标页码按钮')
          }
        } else {
          console.warn('未找到分页组件')
        }

        console.groupEnd()
      }
    }
  },
  // 添加开发环境下的全局方法
  beforeMount() {
    if (process.env.NODE_ENV === 'development') {
      // 将测试方法挂载到window对象，方便在控制台调用
      window.testCostPriceAnomalyPagination = this.testPagination
      window.testCostPriceAnomalyPageUpdate = this.testPageParamUpdate
      window.checkCostPriceAnomalyPaginationStatus = this.checkPaginationStatus
      window.simulateCostPriceAnomalyPageClick = this.simulatePageClick

      console.log('🛠️ 开发环境测试方法已注册：')
      console.log('- window.testCostPriceAnomalyPagination() - 测试分页切换')
      console.log('- window.testCostPriceAnomalyPageUpdate() - 测试参数更新')
      console.log('- window.checkCostPriceAnomalyPaginationStatus() - 检查分页状态')
      console.log('- window.simulateCostPriceAnomalyPageClick(页码) - 模拟点击分页按钮')
    }
  }
}
</script>

<style scoped>
.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.statistic-item {
  text-align: center;
  padding: 10px;
}

.statistic-value {
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 5px;
}

.statistic-label {
  font-size: 14px;
  color: #909399;
}

.item-info {
  display: flex;
  align-items: center;
}

.item-details {
  flex: 1;
  text-align: left;
}

.item-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.item-tag {
  font-size: 12px;
  color: #909399;
}

.price-info {
  text-align: left;
}

.price-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.price-label {
  font-size: 12px;
  color: #909399;
}

.price-value {
  font-weight: 500;
}

.cost-price {
  color: #F56C6C;
}

.recycle-price {
  color: #67C23A;
}

.difference-info {
  text-align: center;
}

.difference-amount {
  margin-bottom: 4px;
}

.difference-percentage {
  font-size: 12px;
  color: #909399;
}

.box-info {
  text-align: left;
  padding: 4px 0;
}

.box-count {
  margin-bottom: 6px;
}

.box-names {
  margin-bottom: 4px;
}

.box-names-text {
  font-weight: 500;
  color: #303133;
  font-size: 13px;
  line-height: 1.4;
  display: block;
  word-break: break-all;
}

.box-types {
  margin-bottom: 4px;
  font-size: 12px;
}

.box-types-label {
  color: #909399;
  font-weight: normal;
}

.box-types-text {
  color: #606266;
}

.box-prices {
  font-size: 12px;
}

.box-prices-label {
  color: #909399;
  font-weight: normal;
}

.box-prices-text {
  color: #E6A23C;
  font-weight: 500;
}

.text-muted {
  color: #C0C4CC;
  font-style: italic;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}

.loading-placeholder {
  color: #C0C4CC;
  font-style: italic;
  font-size: 12px;
  text-align: center;
}
</style>
