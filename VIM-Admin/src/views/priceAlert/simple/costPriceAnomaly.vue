<template>
  <div class="app-container">
    <!-- 页面标题和统计信息 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span class="card-title">成本价异常商品管理</span>
            <el-button 
              style="float: right; padding: 3px 0" 
              type="text" 
              @click="loadStatistics"
              :loading="statisticsLoading"
            >
              刷新统计
            </el-button>
          </div>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">{{ statistics.totalAnomalyItems || 0 }}</div>
                <div class="statistic-label">异常商品总数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">¥{{ statistics.avgPriceDifference || '0.00' }}</div>
                <div class="statistic-label">平均价格差异</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">¥{{ statistics.maxPriceDifference || '0.00' }}</div>
                <div class="statistic-label">最大价格差异</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="statistic-item">
                <div class="statistic-value">{{ Object.keys(statistics.tagStatistics || {}).length }}</div>
                <div class="statistic-label">涉及商品类型</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索表单 -->
    <el-card class="box-card mb-4">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="商品名称" prop="itemName">
          <el-input
            v-model="queryParams.itemName"
            placeholder="请输入商品名称"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="盲盒名称" prop="boxName">
          <el-input
            v-model="queryParams.boxName"
            placeholder="请输入盲盒名称"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="商品标签" prop="itemTag">
          <el-input
            v-model="queryParams.itemTag"
            placeholder="请输入商品标签"
            clearable
            style="width: 200px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 工具栏 -->
    <el-row :gutter="10" class="mb-8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['priceAlert:simple:anomaly:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table 
      v-loading="loading" 
      :data="anomalyList" 
      @selection-change="handleSelectionChange"
      :default-sort="{prop: 'priceDifference', order: 'descending'}"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="商品信息" align="center" min-width="200">
        <template #default="scope">
          <div v-if="scope.row" class="item-info">
            <el-image
              :src="scope.row.itemImage"
              :preview-src-list="[scope.row.itemImage]"
              fit="cover"
              style="width: 40px; height: 40px; border-radius: 4px; margin-right: 10px;"
              :preview-teleported="true"
            >
              <template #error>
                <div class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </template>
            </el-image>
            <div class="item-details">
              <div class="item-name">{{ scope.row.itemName || '未知商品' }}</div>
              <div class="item-tag">{{ scope.row.itemTag || '未分类' }}</div>
            </div>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="价格信息" align="center" min-width="180">
        <template #default="scope">
          <div v-if="scope.row" class="price-info">
            <div class="price-row">
              <span class="price-label">成本价:</span>
              <span class="price-value cost-price">¥{{ scope.row.priceCost || '0.00' }}</span>
            </div>
            <div class="price-row">
              <span class="price-label">回收价:</span>
              <span class="price-value recycle-price">¥{{ scope.row.priceRecycle || '0.00' }}</span>
            </div>
            <div class="price-row">
              <span class="price-label">展示价:</span>
              <span class="price-value">¥{{ scope.row.priceShow || '0.00' }}</span>
            </div>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="价格差异" align="center" prop="priceDifference" sortable min-width="120">
        <template #default="scope">
          <div v-if="scope.row" class="difference-info">
            <div class="difference-amount">
              <el-tag type="danger" size="small">
                ¥{{ scope.row.priceDifference || '0.00' }}
              </el-tag>
            </div>
            <div class="difference-percentage">
              {{ scope.row.differencePercentage || '0.00' }}%
            </div>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="所属盲盒" align="center" min-width="150">
        <template #default="scope">
          <div v-if="scope.row">
            <div v-if="scope.row.boxName" class="box-info">
              <div class="box-name">{{ scope.row.boxName }}</div>
              <div class="box-type">{{ scope.row.boxType || '未知类型' }}</div>
              <div class="box-price">¥{{ scope.row.boxPrice || '0.00' }}</div>
            </div>
            <span v-else class="text-muted">未关联盲盒</span>
          </div>
          <div v-else class="loading-placeholder">数据加载中...</div>
        </template>
      </el-table-column>
      <el-table-column label="商品状态" align="center" width="100">
        <template #default="scope">
          <div v-if="scope.row">
            <el-tag :type="scope.row.itemSale === 1 ? 'success' : 'danger'" size="small">
              {{ scope.row.itemSale === 1 ? '上架' : '下架' }}
            </el-tag>
          </div>
          <div v-else class="loading-placeholder">-</div>
        </template>
      </el-table-column>
      <el-table-column label="库存" align="center" prop="itemStock" width="80" />
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getCostPriceAnomalyList, getCostPriceAnomalyStatistics } from '@/api/priceAlert/simple'

export default {
  name: 'CostPriceAnomaly',
  data() {
    return {
      // 遮罩层
      loading: true,
      // 统计信息加载状态
      statisticsLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 异常商品表格数据
      anomalyList: [],
      // 统计信息
      statistics: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        itemName: null,
        boxName: null,
        itemTag: null
      }
    }
  },
  created() {
    console.log('组件创建，初始查询参数：', this.queryParams)
    this.getList()
    this.loadStatistics()
  },
  mounted() {
    console.log('组件挂载完成，当前查询参数：', this.queryParams)
    console.log('分页组件测试 - 可在控制台调用 window.testCostPriceAnomalyPagination() 进行测试')
  },
  watch: {
    // 监听分页参数变化
    'queryParams.pageNum': function(newVal, oldVal) {
      console.log('页码变化：', oldVal, '->', newVal)
    },
    'queryParams.pageSize': function(newVal, oldVal) {
      console.log('每页大小变化：', oldVal, '->', newVal)
    }
  },
  methods: {
    /** 查询异常商品列表 */
    getList() {
      this.loading = true
      console.log('开始获取成本价异常商品列表，查询参数：', this.queryParams)

      getCostPriceAnomalyList(this.queryParams).then(response => {
        console.log('API响应数据：', response)

        // 验证响应数据结构
        if (response && typeof response === 'object') {
          // 处理不同的响应格式
          if (response.rows && Array.isArray(response.rows)) {
            this.anomalyList = response.rows
            this.total = response.total || 0
          } else if (response.data && Array.isArray(response.data)) {
            this.anomalyList = response.data
            this.total = response.total || response.data.length
          } else if (Array.isArray(response)) {
            this.anomalyList = response
            this.total = response.length
          } else {
            console.warn('未知的响应数据格式：', response)
            this.anomalyList = []
            this.total = 0
          }
        } else {
          console.error('无效的响应数据：', response)
          this.anomalyList = []
          this.total = 0
        }

        console.log('处理后的数据列表：', this.anomalyList)
        console.log('数据总数：', this.total)

        // 验证数据完整性
        if (this.anomalyList.length > 0) {
          const sampleItem = this.anomalyList[0]
          console.log('示例数据项：', sampleItem)

          const requiredFields = ['itemId', 'itemName', 'priceCost', 'priceRecycle', 'priceDifference']
          const missingFields = requiredFields.filter(field =>
            sampleItem[field] === undefined || sampleItem[field] === null
          )

          if (missingFields.length > 0) {
            console.warn('数据缺少必要字段：', missingFields)
          }
        }

        this.loading = false
      }).catch(error => {
        console.error('获取成本价异常商品列表失败：', error)
        this.$modal.msgError('获取数据失败：' + (error.message || '未知错误'))
        this.anomalyList = []
        this.total = 0
        this.loading = false
      })
    },
    /** 加载统计信息 */
    loadStatistics() {
      this.statisticsLoading = true
      console.log('开始加载统计信息')

      getCostPriceAnomalyStatistics().then(response => {
        console.log('统计信息API响应：', response)

        if (response && typeof response === 'object') {
          this.statistics = response.data || response || {}
        } else {
          console.warn('统计信息响应格式异常：', response)
          this.statistics = {}
        }

        console.log('处理后的统计信息：', this.statistics)
        this.statisticsLoading = false
      }).catch(error => {
        console.error('加载统计信息失败：', error)
        this.$modal.msgError('加载统计信息失败：' + (error.message || '未知错误'))
        this.statistics = {}
        this.statisticsLoading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      console.log('执行搜索，重置页码为1')
      this.queryParams.pageNum = 1
      console.log('搜索参数：', this.queryParams)
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      console.log('重置搜索条件')
      this.resetForm('queryForm')
      this.queryParams.pageNum = 1
      this.queryParams.itemName = null
      this.queryParams.boxName = null
      this.queryParams.itemTag = null
      console.log('重置后的参数：', this.queryParams)
      this.getList()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.itemId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('priceAlert/simple/cost-price-anomaly/export', {
        ...this.queryParams
      }, `成本价异常商品_${new Date().getTime()}.xlsx`)
    },
    /** 测试分页功能 */
    testPagination() {
      if (process.env.NODE_ENV === 'development') {
        console.group('🧪 分页功能测试')
        console.log('当前页码：', this.queryParams.pageNum)
        console.log('每页大小：', this.queryParams.pageSize)
        console.log('总记录数：', this.total)
        console.log('当前数据条数：', this.anomalyList.length)
        console.log('分页组件是否显示：', this.total > 0)

        // 模拟分页切换
        const testPage = this.queryParams.pageNum === 1 ? 2 : 1
        console.log(`模拟切换到第${testPage}页`)
        this.queryParams.pageNum = testPage
        this.getList()

        console.groupEnd()
      }
    }
  },
  // 添加开发环境下的全局方法
  beforeMount() {
    if (process.env.NODE_ENV === 'development') {
      // 将测试方法挂载到window对象，方便在控制台调用
      window.testCostPriceAnomalyPagination = this.testPagination
    }
  }
}
</script>

<style scoped>
.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.statistic-item {
  text-align: center;
  padding: 10px;
}

.statistic-value {
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 5px;
}

.statistic-label {
  font-size: 14px;
  color: #909399;
}

.item-info {
  display: flex;
  align-items: center;
}

.item-details {
  flex: 1;
  text-align: left;
}

.item-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.item-tag {
  font-size: 12px;
  color: #909399;
}

.price-info {
  text-align: left;
}

.price-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.price-label {
  font-size: 12px;
  color: #909399;
}

.price-value {
  font-weight: 500;
}

.cost-price {
  color: #F56C6C;
}

.recycle-price {
  color: #67C23A;
}

.difference-info {
  text-align: center;
}

.difference-amount {
  margin-bottom: 4px;
}

.difference-percentage {
  font-size: 12px;
  color: #909399;
}

.box-info {
  text-align: left;
}

.box-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.box-type {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.box-price {
  font-size: 12px;
  color: #E6A23C;
}

.text-muted {
  color: #C0C4CC;
  font-style: italic;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}

.loading-placeholder {
  color: #C0C4CC;
  font-style: italic;
  font-size: 12px;
  text-align: center;
}
</style>
