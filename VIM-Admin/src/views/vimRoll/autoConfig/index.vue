<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="配置名称" prop="configName">
        <el-input
          v-model="queryParams.configName"
          placeholder="请输入配置名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="enabled">
        <el-select v-model="queryParams.enabled" placeholder="请选择状态" clearable>
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['vimRoll:autoRollConfig:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['vimRoll:autoRollConfig:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['vimRoll:autoRollConfig:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['vimRoll:autoRollConfig:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="configList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="配置ID" align="center" prop="id" width="80" />
      <el-table-column label="配置名称" align="center" prop="configName" />
      <el-table-column label="标题模板" align="center" prop="titleTemplate" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="enabled" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
            {{ scope.row.enabled ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="最大人数" align="center" prop="maxUsers" width="100" />
      <el-table-column label="开奖时间" align="center" prop="drawTime" width="180">
        <template #default="scope">
          <span>{{ formatTime(scope.row.drawTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ formatTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
<!--          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['vimRoll:autoRollConfig:query']">详情</el-button>-->
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['vimRoll:autoRollConfig:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['vimRoll:autoRollConfig:remove']">删除</el-button>
          <el-button 
            link 
            :type="scope.row.enabled ? 'warning' : 'success'" 
            :icon="scope.row.enabled ? 'VideoPause' : 'VideoPlay'"
            @click="handleToggle(scope.row)" 
            v-hasPermi="['vimRoll:autoRollConfig:edit']"
          >{{ scope.row.enabled ? '禁用' : '启用' }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改自动化配置对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <!-- 步骤组件 -->
      <el-steps :active="activeStep" finish-status="success" simple style="margin-bottom: 20px;">
        <el-step title="基本信息" />
        <el-step title="参与条件" />
        <el-step title="奖品管理" />
        <el-step title="确认提交" />
      </el-steps>

      <el-form ref="configRef" :model="form" :rules="dynamicRules" label-width="120px">
        <!-- 第一步：基本信息 -->
        <div v-show="activeStep === 0">
          <el-form-item label="配置名称" prop="configName">
            <el-input v-model="form.configName" placeholder="请输入配置名称" />
          </el-form-item>
          <el-form-item label="标题模板" prop="titleTemplate">
            <el-input v-model="form.titleTemplate" placeholder="请输入标题模板，如：每日福利抽奖-{date}" />
            <div class="form-tip">
              <small>支持变量：{date}=当前日期, {time}=当前时间</small>
            </div>
          </el-form-item>
          <el-form-item label="说明模板" prop="infoTemplate">
            <el-input v-model="form.infoTemplate" type="textarea" placeholder="请输入说明模板" />
          </el-form-item>
          <el-form-item label="是否启用" prop="enabled">
            <el-switch v-model="form.enabled" />
          </el-form-item>
          <el-form-item label="最大人数" prop="maxUsers">
            <el-input-number v-model="form.maxUsers" :min="1" :max="1000" placeholder="请输入最大人数" />
          </el-form-item>
          <el-form-item label="开奖时间模式" prop="drawTimeMode">
            <el-radio-group v-model="form.drawTimeMode" @change="onDrawTimeModeChange">
              <el-radio label="smart">智能相对时间（推荐）</el-radio>
              <el-radio label="delay">延迟时间</el-radio>
            </el-radio-group>
            <div class="form-tip">
              <small style="color: #909399;">
                智能相对时间：根据配置的时间点，每次执行时自动调整到合适的未来时间<br>
                延迟时间：从任务执行时间开始，延迟指定分钟后开奖
              </small>
            </div>
          </el-form-item>

          <!-- 智能相对时间配置 -->
          <el-form-item v-if="form.drawTimeMode === 'smart'" label="开奖时间点" prop="drawTime">
            <el-time-picker
              v-model="form.drawTimeValue"
              placeholder="选择开奖时间点"
              format="HH:mm:ss"
              value-format="HH:mm:ss"
              @change="onDrawTimeChange"
            ></el-time-picker>
            <div class="form-tip" v-if="form.drawTimeValue">
              <small>配置的时间点：{{ form.drawTimeValue }}</small>
              <br>
              <small style="color: #67C23A;">
                示例：配置为 00:00:00，每天10点执行任务时，会创建当天24点开奖的Roll房
              </small>
            </div>
            <div class="form-tip">
              <small style="color: #909399;">
                系统会根据任务执行时间，自动调整到下一个对应的时间点
              </small>
            </div>
          </el-form-item>

          <!-- 延迟时间配置 -->
          <el-form-item v-if="form.drawTimeMode === 'delay'" label="延迟时间" prop="drawDelayMinutes">
            <el-input-number
              v-model="form.drawDelayMinutes"
              :min="30"
              :max="1440"
              placeholder="延迟分钟数"
              style="width: 200px;"
            ></el-input-number>
            <span style="margin-left: 10px;">分钟</span>
            <div class="form-tip" v-if="form.drawDelayMinutes">
              <small>延迟时间：{{ form.drawDelayMinutes }} 分钟（{{ Math.floor(form.drawDelayMinutes / 60) }}小时{{ form.drawDelayMinutes % 60 }}分钟）</small>
            </div>
            <div class="form-tip">
              <small style="color: #909399;">从任务执行时间开始计算，最少30分钟，最多24小时</small>
            </div>
          </el-form-item>
        </div>

        <!-- 第二步：参与条件 -->
        <div v-show="activeStep === 1">
          <div v-if="form.rulerConfigs.length === 0" class="empty-rulers">
            <el-empty description="暂无参与条件">
              <el-button type="primary" @click="addRuler">添加条件</el-button>
            </el-empty>
          </div>
          <div v-for="(ruler, index) in form.rulerConfigs" :key="index" class="ruler-container">
            <el-row :gutter="10">
              <el-col :span="24">
                <el-form-item :label="'条件' + (index + 1)" :prop="'rulerConfigs.' + index + '.type'" :rules="[{ required: true, message: '条件类型不能为空', trigger: 'change' }]">
                  <el-select
                    v-model="ruler.type"
                    placeholder="条件类型"
                    style="width: 100%;"
                    @change="handleRulerTypeChange(index)"
                  >
                    <el-option :value="0" label="零门槛"></el-option>
                    <el-option :value="1" label="注册日期"></el-option>
                    <el-option :value="2" label="日充值金额"></el-option>
                    <el-option :value="3" label="周充值金额"></el-option>
                    <el-option :value="4" label="月充值金额"></el-option>
                    <el-option :value="5" label="上级用户id"></el-option>
                    <el-option :value="6" label="私密房间"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="ruler.type === 1">
                <el-form-item :prop="'rulerConfigs.' + index + '.value'" :rules="[{ required: true, message: '请选择注册日期', trigger: 'change' }]">
                  <el-date-picker v-model="ruler.value" type="date" placeholder="注册日期" value-format="X" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="ruler.type === 2 || ruler.type === 3 || ruler.type === 4">
                <el-form-item :prop="'rulerConfigs.' + index + '.value'" :rules="[{ required: true, message: '请输入充值金额', trigger: 'blur' }]">
                  <el-input-number v-model="ruler.value" :min="1" :precision="0" placeholder="最低充值金额" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-button type="danger" icon="Delete" @click="removeRuler(index)">删除</el-button>
              </el-col>
            </el-row>
          </div>
          <div style="margin-top: 15px;">
            <el-button type="primary" icon="Plus" @click="addRuler">添加条件</el-button>
          </div>
        </div>

        <!-- 第三步：奖品管理 -->
        <div v-show="activeStep === 2">
          <div class="search-box" style="margin-bottom: 20px;">
            <el-input
              v-model="searchItemName"
              placeholder="请输入商品名称搜索"
              clearable
              style="width: 300px;"
              @keyup.enter="searchItems"
            >
              <template #append>
                <el-button icon="Search" @click="searchItems"></el-button>
              </template>
            </el-input>
          </div>

          <!-- 商品列表 -->
          <div class="commodity-container">
            <el-row :gutter="12">
              <el-col :span="4" v-for="(item, index) in commodityList" :key="index">
                <el-card shadow="hover" class="commodity-card" :body-style="{ padding: '8px' }">
                  <div class="card-operation">
                    <el-button type="primary" circle size="small" icon="Plus" @click="showAddItemDialog(item)"></el-button>
                  </div>
                  <div class="commodity-image">
                    <el-image 
                      v-if="item.image" 
                      :src="item.image" 
                      style="width: 100%; max-height: 80px; object-fit: contain;"
                    />
                    <div v-else class="no-image">无图片</div>
                  </div>
                  <div class="commodity-info">
                    <div class="commodity-name" :title="item.name">{{ item.name }}</div>
                    <div class="commodity-price">¥{{ item.priceShow }}</div>
                    <div class="commodity-id">ID: {{ item.id }}</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <!-- 商品分页 -->
          <pagination
            v-show="commodityTotal > 0"
            :total="commodityTotal"
            v-model:page="commodityQuery.pageNum"
            v-model:limit="commodityQuery.pageSize"
            @pagination="loadCommodityList"
            style="margin-top: 15px;"
          />

          <el-divider content-position="center" style="margin: 30px 0;">已选奖品列表</el-divider>
          
          <!-- 已选奖品 -->
          <div v-if="form.itemConfigs.length === 0" class="empty-items">
            <el-empty description="暂无奖品">
              <span>请从上方商品列表中添加奖品</span>
            </el-empty>
          </div>
          <el-row :gutter="12" v-else>
            <el-col :span="4" v-for="(item, index) in form.itemConfigs" :key="index">
              <el-card shadow="hover" class="commodity-card" :body-style="{ padding: '8px' }">
                <div class="card-operation">
                  <el-button type="danger" circle size="small" icon="Delete" @click="removeItem(index)"></el-button>
                </div>
                <div class="commodity-image">
                  <el-image 
                    v-if="item.image" 
                    :src="item.image" 
                    style="width: 100%; max-height: 80px; object-fit: contain;"
                  />
                  <div v-else class="no-image">无图片</div>
                </div>
                <div class="commodity-info">
                  <div class="commodity-name" :title="item.itemName">{{ item.itemName }}</div>
                  <div class="commodity-price">等级: {{ item.itemLevel }}</div>
                  <div class="commodity-count" style="margin-top: 5px;">
                    <el-tag type="warning" size="small">数量: {{ item.count }}</el-tag>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 第四步：确认提交 -->
        <div v-show="activeStep === 3">
          <el-card class="summary-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <span>自动化配置信息预览</span>
              </div>
            </template>
            <div class="summary-content">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-card shadow="never" class="inner-card">
                    <template #header>
                      <div class="inner-card-header">
                        <span>基本信息</span>
                      </div>
                    </template>
                    <div class="info-item">
                      <span class="label">配置名称：</span>
                      <span class="value">{{ form.configName }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">标题模板：</span>
                      <span class="value">{{ form.titleTemplate }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">说明模板：</span>
                      <span class="value">{{ form.infoTemplate }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">配置状态：</span>
                      <span class="value">
                        <el-tag :type="form.enabled ? 'success' : 'danger'">
                          {{ form.enabled ? '启用' : '禁用' }}
                        </el-tag>
                      </span>
                    </div>
                    <div class="info-item">
                      <span class="label">人数限制：</span>
                      <span class="value">{{ form.maxUsers }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">开奖时间：</span>
                      <span class="value">{{ formatTime(form.drawTime) }}</span>
                    </div>
                  </el-card>
                </el-col>

                <el-col :span="12">
                  <el-card shadow="never" class="inner-card">
                    <template #header>
                      <div class="inner-card-header">
                        <span>参与条件</span>
                      </div>
                    </template>
                    <div v-if="form.rulerConfigs.length === 0" class="empty-info">
                      <el-empty description="暂无参与条件" :image-size="60"></el-empty>
                    </div>
                    <div v-else class="ruler-list-preview">
                      <div v-for="(ruler, index) in form.rulerConfigs" :key="index" class="info-item ruler-item">
                        <el-tag :type="getRulerTypeTagType(ruler.type)" size="small" style="margin-right: 10px;">
                          {{ getRulerTypeLabel(ruler.type) }}
                        </el-tag>
                        <span class="value" v-if="ruler.type === 1">注册时间 {{ formatTime(ruler.value) }} 之前</span>
                        <span class="value" v-else-if="ruler.type === 2">日充值金额 ≥ {{ ruler.value }}</span>
                        <span class="value" v-else-if="ruler.type === 3">周充值金额 ≥ {{ ruler.value }}</span>
                        <span class="value" v-else-if="ruler.type === 4">月充值金额 ≥ {{ ruler.value }}</span>
                        <span class="value" v-else-if="ruler.type === 0">无门槛</span>
                        <span class="value" v-else-if="ruler.type === 5">上级用户id</span>
                        <span class="value" v-else-if="ruler.type === 6">私密房间</span>
                      </div>
                    </div>
                  </el-card>
                </el-col>
              </el-row>

              <el-card shadow="never" class="inner-card prize-card" style="margin-top: 10px;">
                <template #header>
                  <div class="inner-card-header">
                    <span>奖品列表</span>
                  </div>
                </template>
                <div v-if="form.itemConfigs.length === 0" class="empty-info">
                  <el-empty description="暂无奖品" :image-size="60"></el-empty>
                </div>
                <el-row :gutter="16" v-else>
                  <el-col :span="4" v-for="(item, index) in form.itemConfigs" :key="index">
                    <el-card shadow="hover" class="prize-item-card" :body-style="{ padding: '8px' }">
                      <div class="prize-image">
                        <el-image
                          v-if="item.image"
                          :src="item.image"
                          style="width: 100%; max-height: 80px; object-fit: contain;"
                        />
                        <div v-else class="no-image">无图片</div>
                      </div>
                      <div class="prize-info">
                        <div class="prize-name" :title="item.itemName">{{ item.itemName }}</div>
                        <div class="prize-level">等级: {{ item.itemLevel }}</div>
                        <div class="prize-count" style="margin-top: 5px;">
                          <el-tag size="small" type="warning">数量: {{ item.count }}</el-tag>
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </el-card>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="prevStep" v-if="activeStep > 0">上一步</el-button>
          <el-button type="primary" @click="nextStep" v-if="activeStep < 3">下一步</el-button>
          <el-button type="success" @click="submitForm" v-if="activeStep === 3">提交</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加商品对话框 -->
    <el-dialog title="添加商品为奖品" v-model="addItemDialogVisible" width="400px">
      <el-form :model="addItemForm" label-width="80px">
        <el-form-item label="商品名称">
          <el-input v-model="addItemForm.name" disabled />
        </el-form-item>
        <el-form-item label="奖品等级" required>
          <el-input-number v-model="addItemForm.level" :min="1" :precision="0" style="width: 100%;" />
        </el-form-item>
        <el-form-item label="奖品数量" required>
          <el-input-number v-model="addItemForm.count" :min="1" :precision="0" style="width: 100%;" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addItemDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAddItem">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AutoRollConfig">
import { ref, reactive, toRefs, computed, getCurrentInstance, onMounted, watch } from 'vue';
import { listAutoRollConfigs, getAutoRollConfig, delAutoRollConfig, addAutoRollConfig, updateAutoRollConfig, toggleAutoRollConfig } from "@/api/vimRoll/autoConfig";
import { listCommoditys } from "@/api/commoditySys/commoditys";

const { proxy } = getCurrentInstance();

const configList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 步骤相关
const activeStep = ref(0);

// 条件类型选项
const ruler_type_options = [
  { value: 0, label: '零门槛', type: 'success' },
  { value: 1, label: '注册日期', type: 'primary' },
  { value: 2, label: '日充值金额', type: 'warning' },
  { value: 3, label: '周充值金额', type: 'warning' },
  { value: 4, label: '月充值金额', type: 'warning' },
  { value: 5, label: '上级用户id', type: 'info' },
  { value: 6, label: '私密房间', type: 'info' }
];

// 表单参数
const data = reactive({
  form: {
    id: null,
    configName: null,
    titleTemplate: null,
    infoTemplate: null,
    enabled: true,
    maxUsers: 100,
    drawTime: null,
    itemConfigs: [],  // 奖品配置列表
    rulerConfigs: []  // 条件配置列表
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    configName: null,
    enabled: null
  },
  rules: {
    configName: [
      { required: true, message: "配置名称不能为空", trigger: "blur" }
    ],
    titleTemplate: [
      { required: true, message: "标题模板不能为空", trigger: "blur" }
    ],
    maxUsers: [
      { required: true, message: "最大人数不能为空", trigger: "blur" }
    ],
    drawTimeMode: [
      { required: true, message: "请选择开奖时间模式", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 动态验证规则
const dynamicRules = computed(() => {
  const baseRules = {
    configName: [
      { required: true, message: "配置名称不能为空", trigger: "blur" }
    ],
    titleTemplate: [
      { required: true, message: "标题模板不能为空", trigger: "blur" }
    ],
    maxUsers: [
      { required: true, message: "最大人数不能为空", trigger: "blur" }
    ],
    drawTimeMode: [
      { required: true, message: "请选择开奖时间模式", trigger: "change" }
    ]
  };

  // 根据时间模式添加相应的验证规则
  if (form.value.drawTimeMode === 'smart') {
    baseRules.drawTimeValue = [
      { required: true, message: "请选择开奖时间点", trigger: "change" }
    ];
  } else if (form.value.drawTimeMode === 'delay') {
    baseRules.drawDelayMinutes = [
      { required: true, message: "请输入延迟时间", trigger: "blur" },
      { type: 'number', min: 30, max: 1440, message: "延迟时间必须在30-1440分钟之间", trigger: "blur" }
    ];
  }

  return baseRules;
});

// 商品搜索相关
const searchItemName = ref('');
const commodityList = ref([]);
const commodityTotal = ref(0);
const commodityQuery = reactive({
  pageNum: 1,
  pageSize: 10,
  name: ''
});

// 添加商品对话框相关
const addItemDialogVisible = ref(false);
const addItemForm = reactive({
  id: null,
  name: '',
  level: 1,
  count: 1,
  image: ''
});
const currentItem = ref(null);

/** 查询自动化配置列表 */
function getList() {
  loading.value = true;
  listAutoRollConfigs(queryParams.value).then(response => {
    configList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
  activeStep.value = 0;
}

/** 表单重置 */
function reset() {
  form.value = {
    id: null,
    configName: null,
    titleTemplate: null,
    infoTemplate: null,
    enabled: true,
    maxUsers: 100,
    drawTimeMode: 'smart', // 默认使用智能相对时间
    drawTime: null,
    drawTimeValue: '00:00:00', // 默认零点
    drawDelayMinutes: 60,
    itemConfigs: [],
    rulerConfigs: []
  };
  activeStep.value = 0;
  proxy.resetForm("configRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加自动化配置";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value[0];
  getAutoRollConfig(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改自动化配置";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  handleUpdate(row);
  title.value = "查看自动化配置";
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["configRef"].validate(valid => {
    if (valid) {
      // 准备提交数据
      const submitData = { ...form.value };

      // 根据时间模式处理数据
      if (submitData.drawTimeMode === 'smart') {
        // 智能相对时间模式：确保drawTime字段有值，清空drawDelayMinutes
        if (submitData.drawTimeValue) {
          updateDrawTimeFromValue();
          submitData.drawTime = form.value.drawTime;
        }
        // 明确设置为null，避免后端验证失败
        submitData.drawDelayMinutes = null;

        console.log('智能相对时间模式 - drawTime:', submitData.drawTime, 'drawDelayMinutes:', submitData.drawDelayMinutes);
      } else if (submitData.drawTimeMode === 'delay') {
        // 延迟时间模式：清空drawTime，保留drawDelayMinutes
        submitData.drawTime = null;

        console.log('延迟时间模式 - drawTime:', submitData.drawTime, 'drawDelayMinutes:', submitData.drawDelayMinutes);
      }

      // 移除前端专用字段
      delete submitData.drawTimeMode;
      delete submitData.drawTimeValue;

      console.log('最终提交数据:', submitData);

      if (submitData.id != null) {
        updateAutoRollConfig(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error('修改失败:', error);
          proxy.$modal.msgError("修改失败: " + (error.msg || error.message || '未知错误'));
        });
      } else {
        addAutoRollConfig(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error('新增失败:', error);
          proxy.$modal.msgError("新增失败: " + (error.msg || error.message || '未知错误'));
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除自动化配置编号为"' + ids + '"的数据项？').then(function() {
    return delAutoRollConfig(ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('vimRoll/autoRollConfig/export', {
    ...queryParams.value
  }, `autoRollConfig_${new Date().getTime()}.xlsx`);
}

/** 启用/禁用配置 */
function handleToggle(row) {
  const action = row.enabled ? '禁用' : '启用';
  proxy.$modal.confirm('确认要' + action + '配置"' + row.configName + '"吗？').then(function() {
    return toggleAutoRollConfig(row.id, !row.enabled);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess(action + "成功");
  }).catch(() => {});
}



/** 添加条件 */
function addRuler() {
  form.value.rulerConfigs.push({
    type: 0,
    value: null
  });
}

/** 移除条件 */
function removeRuler(index) {
  form.value.rulerConfigs.splice(index, 1);
}

/** 处理条件类型变化 */
function handleRulerTypeChange(index) {
  form.value.rulerConfigs[index].value = null;
}

/** 格式化时间 */
function formatTime(timestamp) {
  if (!timestamp) {
    return '-';
  }

  // 确保时间戳是数字类型
  const numTimestamp = Number(timestamp);
  if (isNaN(numTimestamp)) {
    return '-';
  }

  // 判断是秒级还是毫秒级时间戳
  const timeMs = String(numTimestamp).length === 10 ? numTimestamp * 1000 : numTimestamp;

  // 使用本地时区格式化时间
  const date = new Date(timeMs);

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return '-';
  }

  // 格式化为 YYYY-MM-DD HH:mm:ss
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/** 获取条件类型标签类型 */
function getRulerTypeTagType(type) {
  const found = ruler_type_options.find(item => item.value === type);
  return found ? found.type : '';
}

/** 获取条件类型标签文本 */
function getRulerTypeLabel(type) {
  const found = ruler_type_options.find(item => item.value === type);
  return found ? found.label : '未知类型';
}

/** 下一步 */
function nextStep() {
  if (activeStep.value < 3) {
    activeStep.value++;
  }
}

/** 上一步 */
function prevStep() {
  if (activeStep.value > 0) {
    activeStep.value--;
  }
}

/** 禁用过去的日期 */
function disabledDate(time) {
  // 禁用今天之前的日期
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
}

/** 处理开奖时间模式变化 */
function onDrawTimeModeChange(mode) {
  if (mode === 'smart') {
    // 智能相对时间模式，清空延迟时间
    form.value.drawDelayMinutes = null;
    if (!form.value.drawTimeValue) {
      form.value.drawTimeValue = '00:00:00';
    }
    updateDrawTimeFromValue();
  } else if (mode === 'delay') {
    // 延迟时间模式，清空绝对时间
    form.value.drawTime = null;
    form.value.drawTimeValue = null;
    if (!form.value.drawDelayMinutes) {
      form.value.drawDelayMinutes = 60;
    }
  }
}

/** 处理时间点选择变化 */
function onDrawTimeChange(timeValue) {
  if (timeValue && form.value.drawTimeMode === 'smart') {
    updateDrawTimeFromValue();
  }
}

/** 根据时间值更新drawTime字段 */
function updateDrawTimeFromValue() {
  if (form.value.drawTimeValue) {
    // 创建一个今天的日期，设置为选择的时间点
    const today = new Date();
    const [hours, minutes, seconds] = form.value.drawTimeValue.split(':');
    today.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || 0), 0);

    // 转换为秒级时间戳
    form.value.drawTime = Math.floor(today.getTime() / 1000);
  }
}

/** 加载商品列表 */
function loadCommodityList() {
  listCommoditys(commodityQuery).then(response => {
    commodityList.value = response.rows || [];
    commodityTotal.value = response.total;
  }).catch(() => {
    commodityList.value = [];
    commodityTotal.value = 0;
  });
}

/** 搜索商品 */
function searchItems() {
  if (searchItemName.value.trim()) {
    const query = {
      name: searchItemName.value.trim(),
      pageNum: 1,
      pageSize: 16
    };

    listCommoditys(query).then(response => {
      if (response.rows && Array.isArray(response.rows)) {
        commodityList.value = response.rows;
        commodityTotal.value = response.total;
      } else {
        commodityList.value = [];
        commodityTotal.value = 0;
      }

      if (commodityList.value.length === 0) {
        proxy.$modal.msgWarning("未找到相关商品");
      }
    }).catch((error) => {
      proxy.$modal.msgError("搜索出错，请稍后重试");
    });
  } else {
    commodityQuery.name = '';
    commodityQuery.pageNum = 1;
    loadCommodityList();
  }
}

// 监听步骤变化，当进入第三步时加载商品列表
watch(() => activeStep.value, (newVal) => {
  if (newVal === 2) {
    loadCommodityList();
  }
});

/** 显示添加商品对话框 */
function showAddItemDialog(item) {
  currentItem.value = item;
  addItemForm.id = item.id;
  addItemForm.name = item.name;
  addItemForm.level = 1;
  addItemForm.count = 1;
  addItemForm.image = item.image || '';
  addItemDialogVisible.value = true;
}

/** 确认添加商品为奖品 */
function confirmAddItem() {
  const existingItemIndex = form.value.itemConfigs.findIndex(item =>
    item.itemId === addItemForm.id &&
    item.itemLevel === addItemForm.level
  );

  if (existingItemIndex >= 0) {
    const existingItem = form.value.itemConfigs[existingItemIndex];
    existingItem.count += addItemForm.count;
    proxy.$modal.msgSuccess(`已添加${addItemForm.count}个奖品（合并到已有奖品）`);
  } else {
    const newItem = {
      itemId: addItemForm.id,
      itemName: addItemForm.name,
      itemLevel: addItemForm.level,
      image: addItemForm.image,
      count: addItemForm.count
    };
    form.value.itemConfigs.push(newItem);
    proxy.$modal.msgSuccess(`已添加${addItemForm.count}个奖品`);
  }

  addItemDialogVisible.value = false;
}

/** 移除奖品 */
function removeItem(index) {
  const item = form.value.itemConfigs[index];
  if (item.count && item.count > 1) {
    proxy.$confirm('该奖品有多个，您想要如何操作？', '确认信息', {
      distinguishCancelAndClose: true,
      confirmButtonText: '全部移除',
      cancelButtonText: '减少一个',
      type: 'warning'
    }).then(() => {
      form.value.itemConfigs.splice(index, 1);
      proxy.$message({
        type: 'success',
        message: '已移除全部该奖品'
      });
    }).catch(action => {
      if (action === 'cancel') {
        item.count--;
        proxy.$message({
          type: 'info',
          message: `已减少一个奖品，当前数量: ${item.count}`
        });
      }
    });
  } else {
    form.value.itemConfigs.splice(index, 1);
  }
}

// 初始化
onMounted(() => {
  getList();
});
</script>

<style scoped>
.ruler-container {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px dashed #ccc;
  border-radius: 4px;
}

.el-divider {
  margin: 20px 0;
}

.commodity-container {
  margin-bottom: 20px;
}

.commodity-card {
  margin-bottom: 12px;
  height: 175px;
  display: flex;
  flex-direction: column;
  position: relative;
}

.card-operation {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 10;
}

.commodity-image {
  height: 80px;
  max-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-bottom: 5px;
}

.commodity-info {
  padding: 5px 0;
  flex: 1;
}

.commodity-name {
  font-size: 12px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 3px;
}

.commodity-price {
  font-size: 14px;
  color: #f56c6c;
  font-weight: bold;
  margin-bottom: 3px;
}

.commodity-id {
  font-size: 12px;
  color: #909399;
}

.no-image {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #909399;
}

.form-tip {
  margin-top: 5px;
}

.form-tip small {
  color: #909399;
}

/* 确认提交步骤的样式 */
.summary-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-content {
  padding: 10px 0;
}

.inner-card {
  margin-bottom: 10px;
  height: 100%;
}

.inner-card-header {
  font-size: 16px;
  font-weight: bold;
}

.info-item {
  margin-bottom: 8px;
  display: flex;
  align-items: flex-start;
}

.label {
  font-weight: bold;
  width: 100px;
  color: #606266;
}

.value {
  flex: 1;
  word-break: break-all;
}

.empty-info {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.ruler-list-preview {
  max-height: 200px;
  overflow-y: auto;
}

.ruler-item {
  padding: 5px 0;
  border-bottom: 1px dashed #EBEEF5;
}

.ruler-item:last-child {
  border-bottom: none;
}

.prize-item-card {
  margin-bottom: 12px;
  height: 175px;
  display: flex;
  flex-direction: column;
}

.prize-image {
  height: 80px;
  max-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-bottom: 5px;
}

.prize-info {
  padding: 5px 0;
}

.prize-name {
  font-size: 12px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 3px;
}

.prize-level {
  font-size: 12px;
  color: #909399;
  margin-bottom: 3px;
}

.prize-count {
  font-size: 12px;
}

.empty-rulers, .empty-items {
  text-align: center;
  padding: 20px;
}
</style>
