package com.ruoyi.project.VimOrderPaySys.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.VimOrderPaySys.domain.VimOrderPay;
import com.ruoyi.project.VimOrderPaySys.domain.vo.VimOrderPayVO;
import com.ruoyi.project.VimOrderPaySys.service.IVimOrderPayService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 用户电能消费Controller
 * 
 * <AUTHOR>
 * @date 2025-06-29
 */
@Api(tags = "用户电能消费管理")
@RestController
@RequestMapping("/VimOrderPaySys/VimOrderPays")
public class VimOrderPayController extends BaseController
{
    @Autowired
    private IVimOrderPayService vimOrderPayService;

    /**
     * 查询用户电能消费列表
     */
    @ApiOperation(value = "查询用户电能消费列表", notes = "查询用户电能消费列表")
    @PreAuthorize("@ss.hasPermi('VimOrderPaySys:VimOrderPays:list')")
    @GetMapping("/list")
    public TableDataInfo list(VimOrderPay vimOrderPay)
    {
        startPage();
        List<VimOrderPayVO> list = vimOrderPayService.selectVimOrderPayWithUserList(vimOrderPay);
        return getDataTable(list);
    }

    /**
     * 导出用户电能消费列表
     */
    @ApiOperation(value = "导出用户电能消费列表", notes = "导出用户电能消费列表")
    @PreAuthorize("@ss.hasPermi('VimOrderPaySys:VimOrderPays:export')")
    @Log(title = "用户电能消费", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VimOrderPay vimOrderPay)
    {
        List<VimOrderPay> list = vimOrderPayService.selectVimOrderPayList(vimOrderPay);
        ExcelUtil<VimOrderPay> util = new ExcelUtil<VimOrderPay>(VimOrderPay.class);
        util.exportExcel(response, list, "用户电能消费数据");
    }

    /**
     * 获取用户电能消费详细信息
     */
    @ApiOperation(value = "获取用户电能消费详细信息", notes = "根据订单号获取用户电能消费详细信息")
    @PreAuthorize("@ss.hasPermi('VimOrderPaySys:VimOrderPays:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return AjaxResult.success(vimOrderPayService.selectVimOrderPayById(id));
    }

    /**
     * 新增用户电能消费
     */
    @ApiOperation(value = "新增用户电能消费", notes = "新增用户电能消费")
    @PreAuthorize("@ss.hasPermi('VimOrderPaySys:VimOrderPays:add')")
    @Log(title = "用户电能消费", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VimOrderPay vimOrderPay)
    {
        return toAjax(vimOrderPayService.insertVimOrderPay(vimOrderPay));
    }

    /**
     * 修改用户电能消费
     */
    @ApiOperation(value = "修改用户电能消费", notes = "修改用户电能消费")
    @PreAuthorize("@ss.hasPermi('VimOrderPaySys:VimOrderPays:edit')")
    @Log(title = "用户电能消费", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VimOrderPay vimOrderPay)
    {
        return toAjax(vimOrderPayService.updateVimOrderPay(vimOrderPay));
    }

    /**
     * 删除用户电能消费
     */
    @ApiOperation(value = "删除用户电能消费", notes = "删除用户电能消费")
    @PreAuthorize("@ss.hasPermi('VimOrderPaySys:VimOrderPays:remove')")
    @Log(title = "用户电能消费", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(vimOrderPayService.deleteVimOrderPayByIds(ids));
    }
}
