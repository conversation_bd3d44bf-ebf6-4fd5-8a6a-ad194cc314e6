package com.ruoyi.project.VimUserSys.service;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.project.VimUserSys.domain.VimUser;
import com.ruoyi.project.VimUserSys.domain.vo.SubordinateUserVO;
import com.ruoyi.project.commoditySys.domain.VimItem;

/**
 * 盲盒用户Service接口
 * 
 * <AUTHOR> and 催一催
 * @date 2025-04-17
 */
public interface IVimUserService 
{
    /**
     * 查询盲盒用户
     *
     * @param id 盲盒用户主键
     * @return 盲盒用户
     */
    public VimUser selectVimUserById(Long id);

    /**
     * 根据手机号查询盲盒用户
     *
     * @param phone 手机号
     * @return 盲盒用户
     */
    public VimUser selectVimUserByPhone(String phone);

    /**
     * 查询盲盒用户列表
     * 
     * @param vimUser 盲盒用户
     * @return 盲盒用户集合
     */
    public List<VimUser> selectVimUserList(VimUser vimUser);

    /**
     * 新增盲盒用户
     * 
     * @param vimUser 盲盒用户
     * @return 结果
     */
    public int insertVimUser(VimUser vimUser);

    /**
     * 修改盲盒用户
     * 
     * @param vimUser 盲盒用户
     * @return 结果
     */
    public int updateVimUser(VimUser vimUser);

    /**
     * 批量删除盲盒用户
     * 
     * @param ids 需要删除的盲盒用户主键集合
     * @return 结果
     */
    public int deleteVimUserByIds(Long[] ids);

    /**
     * 删除盲盒用户信息
     * 
     * @param id 盲盒用户主键
     * @return 结果
     */
    public int deleteVimUserById(Long id);

    List<VimItem> getBackpack(Long id, String itemName, String sortField);
    
    /**
     * 修改用户状态
     *
     * @param userId 用户ID
     * @param state 用户状态（1正常 2禁用）
     * @return 结果
     */
    public int updateUserState(Long userId, Integer state);

    /**
     * 获取用户当前钥匙数量
     *
     * @param userId 用户ID
     * @return 用户当前钥匙数量
     */
    public BigDecimal getUserKeyAmount(Long userId);

    /**
     * 增加用户钥匙数量
     *
     * @param userId 用户ID
     * @param amount 增加的钥匙数量
     * @return 更新结果
     */
    public int addUserKeyAmount(Long userId, BigDecimal amount);

    /**
     * 修改用户身份类型
     *
     * @param userId 用户ID
     * @param identity 身份类型（1-普通用户,2-主播）
     * @return 更新结果
     */
    public int updateUserIdentity(Long userId, Integer identity);

    /**
     * 查询指定用户的下级用户列表
     *
     * @param inviteUserId 邀请人用户ID
     * @return 下级用户列表
     */
    public List<SubordinateUserVO> selectSubordinateUsers(Long inviteUserId);

    /**
     * 查询指定用户的下级用户列表（手动分页）
     *
     * @param inviteUserId 邀请人用户ID
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public com.ruoyi.common.utils.PageResult<SubordinateUserVO> selectSubordinateUsersWithPagination(Long inviteUserId, int pageNum, int pageSize);

    /**
     * 统计指定用户的下级用户数量
     *
     * @param inviteUserId 邀请人用户ID
     * @return 下级用户数量
     */
    public int countSubordinateUsers(Long inviteUserId);

    boolean resetPassword(Long vimUserId);

}
