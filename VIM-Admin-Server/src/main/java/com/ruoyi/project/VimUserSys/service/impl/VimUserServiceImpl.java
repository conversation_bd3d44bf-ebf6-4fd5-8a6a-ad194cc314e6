package com.ruoyi.project.VimUserSys.service.impl;

import com.ruoyi.project.VimServerSeedSys.service.IVimSeedService;
import com.ruoyi.project.VimUserSys.domain.VimUser;
import com.ruoyi.project.VimUserSys.domain.vo.SubordinateUserVO;
import com.ruoyi.project.VimUserSys.mapper.VimUserMapper;
import com.ruoyi.project.VimUserSys.service.IVimUserService;
import com.ruoyi.project.VimUserSys.enums.UserIdentityEnum;
import com.ruoyi.project.VimUserSys.utils.HashUtil;
import com.ruoyi.project.commoditySys.domain.VimItem;
import com.ruoyi.project.commoditySys.mapper.VimItemMapper;
import com.ruoyi.project.system.domain.SysUser;
import com.ruoyi.project.system.domain.SysRole;
import com.ruoyi.project.system.domain.SysDept;
import com.ruoyi.project.system.service.ISysUserService;
import com.ruoyi.project.system.service.ISysRoleService;
import com.ruoyi.project.system.service.ISysDeptService;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.redis.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 盲盒用户Service业务层处理
 *
 * <AUTHOR> and 催一催
 * @date 2025-04-17
 */
@Slf4j
@Service
public class VimUserServiceImpl implements IVimUserService {
    @Autowired
    private VimUserMapper vimUserMapper;
    
    @Autowired
    private VimItemMapper vimItemMapper;

    @Autowired
    private IVimSeedService vimSeedService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private RedisCache redisCache;


    /**
     * 查询盲盒用户
     *
     * @param id 盲盒用户主键
     * @return 盲盒用户
     */
    @Override
    public VimUser selectVimUserById(Long id) {
        return vimUserMapper.selectVimUserById(id);
    }

    /**
     * 根据手机号查询盲盒用户
     *
     * @param phone 手机号
     * @return 盲盒用户
     */
    @Override
    public VimUser selectVimUserByPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return null;
        }
        VimUser queryUser = new VimUser();
        queryUser.setPhone(phone);
        List<VimUser> users = vimUserMapper.selectVimUserList(queryUser);
        return users.isEmpty() ? null : users.get(0);
    }

    /**
     * 查询盲盒用户列表
     *
     * @param vimUser 盲盒用户
     * @return 盲盒用户
     */
    @Override
    public List<VimUser> selectVimUserList(VimUser vimUser) {
        return vimUserMapper.selectVimUserList(vimUser);
    }

    /**
     * 新增盲盒用户
     *
     * @param vimUser 盲盒用户
     * @return 结果
     */
    @Override
    public int insertVimUser(VimUser vimUser) {
        String hash = HashUtil.hashWithUserName(vimUser.getUsername(), vimUser.getPassword());
        vimUser.setPassword(hash);
        vimUser.setSeed(vimSeedService.generateUserSeed());
        return vimUserMapper.insertVimUser(vimUser);
    }

    /**
     * 修改盲盒用户
     *
     * @param vimUser 盲盒用户
     * @return 结果
     */
    @Override
    public int updateVimUser(VimUser vimUser) {
        return vimUserMapper.updateVimUser(vimUser);
    }

    /**
     * 批量删除盲盒用户
     *
     * @param ids 需要删除的盲盒用户主键
     * @return 结果
     */
    @Override
    public int deleteVimUserByIds(Long[] ids) {
        return vimUserMapper.deleteVimUserByIds(ids);
    }

    /**
     * 删除盲盒用户信息
     *
     * @param id 盲盒用户主键
     * @return 结果
     */
    @Override
    public int deleteVimUserById(Long id) {
        return vimUserMapper.deleteVimUserById(id);
    }

    @Override
    public List<VimItem> getBackpack(Long id,  String itemName, String sortField) {
        return vimItemMapper.selectVimItemByUserId(id, itemName, sortField);
    }
    
    /**
     * 修改用户状态
     *
     * @param userId 用户ID
     * @param state 用户状态（1正常 2禁用）
     * @return 结果
     */
    @Override
    public int updateUserState(Long userId, Integer state) {
        if (state != 1 && state != 2) {
            throw new RuntimeException("用户状态参数不正确，只能为1（正常）或2（禁用）");
        }

        VimUser user = new VimUser();
        user.setId(userId);
        user.setState(state);

        // 根据用户状态设置isauth字段
        if (state == 2) {
            // 禁用用户时，直接设置为未实名
            user.setIsauth(0);
            log.info("禁用用户：userId={}, state={}, isauth=0（强制设为未实名）", userId, state);
        } else {
            // 启用用户时，根据vim_user_auth表中的实名认证记录来判断
            int authCount = vimUserMapper.checkUserAuthStatus(userId);
            boolean hasAuth = authCount > 0;
            user.setIsauth(hasAuth ? 1 : 0);

            log.info("启用用户：userId={}, state={}, 实名认证记录数={}, isauth={}",
                    userId, state, authCount, user.getIsauth());

            if (hasAuth) {
                log.info("用户{}已有实名认证记录，设置为已实名状态", userId);
            } else {
                log.info("用户{}无实名认证记录，设置为未实名状态", userId);
            }
        }

        return vimUserMapper.updateVimUser(user);
    }

    /**
     * 获取用户当前钥匙数量
     *
     * @param userId 用户ID
     * @return 用户当前钥匙数量
     */
    @Override
    public BigDecimal getUserKeyAmount(Long userId) {
        VimUser user = vimUserMapper.selectVimUserById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        return user.getKey() != null ? user.getKey() : BigDecimal.ZERO;
    }

    /**
     * 增加用户钥匙数量
     *
     * @param userId 用户ID
     * @param amount 增加的钥匙数量
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addUserKeyAmount(Long userId, BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("钥匙数量必须大于0");
        }

        // 获取当前用户信息
        VimUser user = vimUserMapper.selectVimUserById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 计算新的钥匙数量
        BigDecimal currentAmount = user.getKey() != null ? user.getKey() : BigDecimal.ZERO;
        BigDecimal newAmount = currentAmount.add(amount);

        // 更新用户钥匙数量
        VimUser updateUser = new VimUser();
        updateUser.setId(userId);
        updateUser.setKey(newAmount);

        return vimUserMapper.updateVimUser(updateUser);
    }

    /**
     * 修改用户身份类型
     * 当身份切换为线上主播(2)时，自动注册到sys_user表
     * 当身份从线上主播(2)切换为普通用户(1)或线下主播(3)时，删除sys_user表中的记录
     *
     * @param userId 用户ID
     * @param identity 身份类型（1-普通用户,2-线上主播,3-线下主播）
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUserIdentity(Long userId, Integer identity) {
        if (!UserIdentityEnum.isValidCode(identity)) {
            throw new RuntimeException("身份类型参数不正确，只能为1（普通用户）、2（线上主播）或3（线下主播）");
        }

        // 检查用户是否存在
        VimUser user = vimUserMapper.selectVimUserById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 获取旧的身份类型
        Integer oldIdentity = user.getIdentity();

        // 更新用户身份
        VimUser updateUser = new VimUser();
        updateUser.setId(userId);
        updateUser.setIdentity(identity);

        int result = vimUserMapper.updateVimUser(updateUser);

        if (result > 0) {
            log.info("用户身份修改成功: userId={}, oldIdentity={}, newIdentity={}",
                    userId, oldIdentity, identity);

            // 如果身份切换为线上主播(2)，自动注册到sys_user表
            if (UserIdentityEnum.isOnlineAnchor(identity)) {
                try {
                    registerToSysUser(user);
                    log.info("用户自动注册到sys_user表成功: userId={}, phone={}", userId, user.getPhone());
                } catch (Exception e) {
                    log.error("用户自动注册到sys_user表失败: userId={}, phone={}, error={}",
                             userId, user.getPhone(), e.getMessage());
                    // 注册失败时回滚身份修改
                    throw new RuntimeException("身份切换失败：自动注册系统用户失败 - " + e.getMessage());
                }
            } 
            // 如果从线上主播(2)切换为其他身份，删除sys_user表中的记录
            else if (UserIdentityEnum.isOnlineAnchor(oldIdentity)) {
                try {
                    boolean removeResult = removeFromSysUser(user);
                    if (removeResult) {
                        log.info("用户从sys_user表中删除成功: userId={}, phone={}", userId, user.getPhone());
                    } else {
                        log.error("用户从sys_user表中删除失败: userId={}, phone={}", userId, user.getPhone());
                        // 删除失败时回滚身份修改
                        throw new RuntimeException("身份切换失败：从系统用户表删除用户失败");
                    }
                } catch (Exception e) {
                    log.error("从sys_user表中删除用户时发生异常: userId={}, phone={}, error={}",
                             userId, user.getPhone(), e.getMessage());
                    // 删除失败时回滚身份修改
                    throw new RuntimeException("身份切换失败：从系统用户表删除用户失败 - " + e.getMessage());
                }
            }
        } else {
            log.error("用户身份修改失败: userId={}, identity={}", userId, identity);
        }

        return result;
    }

    /**
     * 将vim_user注册到sys_user表
     *
     * @param vimUser vim_user用户信息
     */
    private void registerToSysUser(VimUser vimUser) {
        String phone = vimUser.getPhone();
        if (StringUtils.isEmpty(phone)) {
            throw new RuntimeException("用户手机号为空，无法注册到系统用户表");
        }

        // 检查sys_user表中是否已存在该手机号
        SysUser existingUser = sysUserService.selectUserByUserName(phone);
        if (existingUser != null) {
            log.info("用户已在sys_user表中存在，跳过注册: phone={}", phone);
            return;
        }

        // 验证角色和部门是否存在
        Long defaultRoleId = 105L; // 主播角色ID
        Long defaultDeptId = 201L; // 默认部门ID

        SysRole defaultRole = sysRoleService.selectRoleById(defaultRoleId);
        if (defaultRole == null) {
            throw new RuntimeException("默认角色(ID:" + defaultRoleId + ")不存在，请联系系统管理员");
        }

        SysDept defaultDept = sysDeptService.selectDeptById(defaultDeptId);
        if (defaultDept == null) {
            throw new RuntimeException("默认部门(ID:" + defaultDeptId + ")不存在，请联系系统管理员");
        }

        // 创建sys_user记录
        SysUser sysUser = new SysUser();
        // 将手机号设置为用户名，用于登录认证
        sysUser.setUserName(phone);
        // 使用vim_user的昵称，如果为空则使用手机号后4位
        String nickname = StringUtils.isNotEmpty(vimUser.getNickname()) ?
                         vimUser.getNickname() : "用户" + phone.substring(7);
        sysUser.setNickName(nickname);
        // 将手机号设置为联系电话
        sysUser.setPhonenumber(phone);

        // 生成默认密码：手机号@用户ID，确保密码的唯一性和复杂性
        String defaultPassword = phone + "@" + vimUser.getId();
        // 使用标准BCrypt加密，避免兼容性问题
        sysUser.setPassword(SecurityUtils.encryptPassword(defaultPassword));
        log.info("为用户生成默认密码: userId={}, phone={}, passwordFormat={}@{}",
                vimUser.getId(), phone, phone, vimUser.getId());

        // 设置为正常状态
        sysUser.setStatus("0");
        // 设置默认部门
        sysUser.setDeptId(defaultDeptId);

        // 保存用户
        boolean registerResult = sysUserService.registerUser(sysUser);
        if (!registerResult) {
            throw new RuntimeException("注册到系统用户表失败");
        }

        // 查询新创建的用户获取ID
        SysUser newUser = sysUserService.selectUserByUserName(phone);
        if (newUser == null) {
            throw new RuntimeException("注册失败，无法获取用户信息");
        }

        // 为新用户分配默认角色
        try {
            int roleAssignResult = sysRoleService.insertAuthUsers(defaultRoleId, new Long[]{newUser.getUserId()});
            if (roleAssignResult <= 0) {
                throw new RuntimeException("角色分配失败");
            }
            log.info("用户角色分配成功: userId={}, roleId={}, roleName={}",
                    newUser.getUserId(), defaultRoleId, defaultRole.getRoleName());
        } catch (Exception e) {
            log.error("用户角色分配失败: userId={}, roleId={}, error={}",
                     newUser.getUserId(), defaultRoleId, e.getMessage());
            throw new RuntimeException("角色分配失败：" + e.getMessage());
        }

        log.info("用户注册到sys_user表成功: vimUserId={}, sysUserId={}, phone={}, deptId={}, roleId={}",
                vimUser.getId(), newUser.getUserId(), phone, defaultDeptId, defaultRoleId);
    }

    /**
     * 将用户从sys_user表中删除
     *
     * @param vimUser vim_user用户信息
     * @return 是否删除成功
     */
    private boolean removeFromSysUser(VimUser vimUser) {
        String phone = vimUser.getPhone();
        if (StringUtils.isEmpty(phone)) {
            log.warn("用户手机号为空，无法从系统用户表中删除: userId={}", vimUser.getId());
            return false;
        }

        // 检查sys_user表中是否存在该手机号
        SysUser existingUser = sysUserService.selectUserByUserName(phone);
        if (existingUser == null) {
            log.info("用户在sys_user表中不存在，无需删除: userId={}, phone={}", vimUser.getId(), phone);
            return true;
        }

        try {
            // 删除用户
            int result = sysUserService.deleteUserById(existingUser.getUserId());
            if (result > 0) {
                log.info("用户从sys_user表中删除成功: vimUserId={}, sysUserId={}, phone={}",
                        vimUser.getId(), existingUser.getUserId(), phone);
                return true;
            } else {
                log.error("用户从sys_user表中删除失败: vimUserId={}, sysUserId={}, phone={}",
                        vimUser.getId(), existingUser.getUserId(), phone);
                return false;
            }
        } catch (Exception e) {
            log.error("删除系统用户时发生异常: userId={}, phone={}, error={}",
                    vimUser.getId(), phone, e.getMessage());
            return false;
        }
    }

    /**
     * 查询指定用户的下级用户列表
     *
     * @param inviteUserId 邀请人用户ID
     * @return 下级用户列表
     */
    @Override
    public List<SubordinateUserVO> selectSubordinateUsers(Long inviteUserId) {
        if (inviteUserId == null) {
            throw new RuntimeException("邀请人用户ID不能为空");
        }

        // 检查邀请人是否存在
        VimUser inviteUser = vimUserMapper.selectVimUserById(inviteUserId);
        if (inviteUser == null) {
            throw new RuntimeException("邀请人用户不存在");
        }

        return vimUserMapper.selectSubordinateUsers(inviteUserId);
    }

    /**
     * 查询指定用户的下级用户列表（手动分页）
     *
     * @param inviteUserId 邀请人用户ID
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @return 分页结果
     */
    @Override
    public com.ruoyi.common.utils.PageResult<SubordinateUserVO> selectSubordinateUsersWithPagination(Long inviteUserId, int pageNum, int pageSize) {
        if (inviteUserId == null) {
            throw new RuntimeException("邀请人用户ID不能为空");
        }

        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        if (pageSize > 100) {
            pageSize = 100;
        }

        // 检查邀请人是否存在
        VimUser inviteUser = vimUserMapper.selectVimUserById(inviteUserId);
        if (inviteUser == null) {
            throw new RuntimeException("邀请人用户不存在");
        }

        // 计算分页参数
        int offset = (pageNum - 1) * pageSize;

        // 查询分页数据
        List<SubordinateUserVO> list = vimUserMapper.selectSubordinateUsersWithPagination(inviteUserId, offset, pageSize);

        // 查询总数
        int total = vimUserMapper.countSubordinateUsers(inviteUserId);

        // 返回分页结果
        return com.ruoyi.common.utils.PageResult.build(list, total, pageNum, pageSize);
    }

    /**
     * 统计指定用户的下级用户数量
     *
     * @param inviteUserId 邀请人用户ID
     * @return 下级用户数量
     */
    @Override
    public int countSubordinateUsers(Long inviteUserId) {
        if (inviteUserId == null) {
            throw new RuntimeException("邀请人用户ID不能为空");
        }

        return vimUserMapper.countSubordinateUsers(inviteUserId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(Long vimUserId) {
        try {
            VimUser vimUser = vimUserMapper.selectVimUserById(vimUserId);
            if (vimUser == null) {
                log.error("重置密码失败: 用户不存在, userId={}", vimUserId);
                return false;
            }
            String username = vimUser.getUsername();
            if (StringUtils.isEmpty(username)) {
                log.error("重置密码失败: 用户userName为空, userId={}", vimUserId);
                return false;
            }

            // 生成默认密码（通常是用户名或固定密码）
            // 或者使用 username 作为默认密码
            String defaultPassword = "123456";
            String newPassword = HashUtil.hashWithUserName(username, defaultPassword);
            vimUser.setPassword(newPassword);

            // 更新数据库
            int result = vimUserMapper.updateVimUser(vimUser);
            if (result > 0) {
                log.info("重置密码成功: userId={}, username={}", vimUserId, username);
                return true;
            } else {
                log.error("重置密码失败: 数据库更新失败, userId={}", vimUserId);
                return false;
            }

        } catch (Exception e) {
            log.error("重置密码失败: userId={}, error={}", vimUserId, e.getMessage(), e);
            return false;
        }
    }
}
