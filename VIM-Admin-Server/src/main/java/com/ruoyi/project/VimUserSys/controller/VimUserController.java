package com.ruoyi.project.VimUserSys.controller;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.VimUserSys.domain.VimUser;
import com.ruoyi.project.VimUserSys.domain.dto.UserIdentityUpdateDTO;
import com.ruoyi.project.VimUserSys.domain.vo.SubordinateUserVO;
import com.ruoyi.project.VimUserSys.service.IVimUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 盲盒用户Controller
 *
 * <AUTHOR> and 催一催
 * @date 2025-04-17
 */
@Api(tags = "用户管理", description = "盲盒用户信息的增删改查操作")
@RestController
@RequestMapping("/VimUserSys/VimUsers")
public class VimUserController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(VimUserController.class);

    @Autowired
    private IVimUserService vimUserService;


    /**
     * 查询盲盒用户列表
     */
    @ApiOperation(value = "查询用户列表", notes = "获取盲盒用户分页列表数据")
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:list')")
    @GetMapping("/list")
    public TableDataInfo list(VimUser vimUser) {
        startPage();
        List<VimUser> list = vimUserService.selectVimUserList(vimUser);
        return getDataTable(list);
    }

    /**
     * 导出盲盒用户列表
     */
    @ApiOperation(value = "导出用户列表", notes = "导出盲盒用户数据到Excel文件")
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:export')")
    @Log(title = "盲盒用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VimUser vimUser) {
        List<VimUser> list = vimUserService.selectVimUserList(vimUser);
        ExcelUtil<VimUser> util = new ExcelUtil<VimUser>(VimUser.class);
        util.exportExcel(response, list, "盲盒用户数据");
    }
    /**
     * 获取盲盒用户详细信息
     */
    @ApiOperation(value = "获取用户详情", notes = "根据用户ID获取详细信息")
    @ApiImplicitParam(name = "uid", value = "用户ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @GetMapping(value = "/{uid}")
    public AjaxResult getInfo(@PathVariable("uid") Long id) {
        return success(vimUserService.selectVimUserById(id));
    }

    /**
     * 新增盲盒用户
     */
    @ApiOperation(value = "新增用户", notes = "新增盲盒用户信息")
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:add')")
    @Log(title = "盲盒用户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VimUser vimUser) {
        return toAjax(vimUserService.insertVimUser(vimUser));
    }

    /**
     * 修改盲盒用户
     */
    @ApiOperation(value = "修改用户", notes = "修改盲盒用户信息")
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:edit')")
    @Log(title = "盲盒用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VimUser vimUser) {
        return toAjax(vimUserService.updateVimUser(vimUser));
    }

    /**
     * 删除盲盒用户
     */
    @ApiOperation(value = "删除用户", notes = "根据ID数组批量删除盲盒用户")
    @ApiImplicitParam(name = "ids", value = "用户ID数组", required = true, dataType = "Long[]", paramType = "path", example = "1,2,3")
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:remove')")
    @Log(title = "盲盒用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(vimUserService.deleteVimUserByIds(ids));
    }

    /**
     * 获取用户背包信息
     */
    @ApiOperation(value = "获取用户背包信息", notes = "根据用户ID获取用户背包信息")
    @ApiImplicitParam(name = "uid", value = "用户ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @GetMapping("/bag/{id}")
    public TableDataInfo getBackpack(@PathVariable("id") Long id, String itemName, String sortField) {
        if (id == null) {
            throw new RuntimeException("用户ID不能为空");
        }
        startPage();
        return getDataTable(vimUserService.getBackpack(id, itemName, sortField));
    }

    /**
     * 修改用户状态
     */
    @ApiOperation(value = "修改用户状态", notes = "修改盲盒用户状态（1正常 2禁用）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path", example = "1"),
            @ApiImplicitParam(name = "state", value = "用户状态", required = true, dataType = "Integer", paramType = "path", example = "1", allowableValues = "1,2")
    })
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:edit')")
    @Log(title = "盲盒用户", businessType = BusinessType.UPDATE)
    @PutMapping("/state/{userId}/{state}")
    public AjaxResult updateUserState(@PathVariable("userId") Long userId, @PathVariable("state") Integer state) {
        return toAjax(vimUserService.updateUserState(userId, state));
    }

    /**
     * 检查用户实名认证状态
     */
    @ApiOperation(value = "检查用户实名认证状态", notes = "检查指定用户是否已进行实名认证")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:query')")
    @GetMapping("/auth-status/{userId}")
    public AjaxResult checkUserAuthStatus(@PathVariable("userId") Long userId) {
        boolean hasAuth = vimUserService.checkUserAuthStatus(userId);
        Map<String, Object> result = new HashMap<>();
        result.put("userId", userId);
        result.put("hasAuth", hasAuth);
        result.put("authStatus", hasAuth ? "已实名" : "未实名");
        return AjaxResult.success("查询成功", result);
    }

    /**
     * 修改用户身份类型
     */
    @ApiOperation(value = "修改用户身份", notes = "修改盲盒用户的身份类型（普通用户/主播）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path", example = "1"),
            @ApiImplicitParam(name = "updateDTO", value = "身份修改信息", required = true, dataType = "UserIdentityUpdateDTO", paramType = "body")
    })
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:edit')")
    @Log(title = "盲盒用户身份修改", businessType = BusinessType.UPDATE)
    @PutMapping("/{userId}/identity")
    public AjaxResult updateUserIdentity(
            @PathVariable("userId") Long userId,
            @RequestBody @Valid UserIdentityUpdateDTO updateDTO) {
        try {
            int result = vimUserService.updateUserIdentity(userId, updateDTO.getIdentity());
            if (result > 0) {
                // 返回更新后的用户信息
                VimUser updatedUser = vimUserService.selectVimUserById(userId);
                return AjaxResult.success("用户身份修改成功", updatedUser);
            } else {
                return AjaxResult.error("用户身份修改失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("用户身份修改失败：" + e.getMessage());
        }
    }

    /**
     * 用户搜索接口
     */
    @ApiOperation(value = "用户搜索", notes = "根据用户ID搜索盲盒用户信息")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "query", example = "1")
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:query')")
    @GetMapping("/search")
    public AjaxResult searchUser(@RequestParam("userId") Long userId) {
        try {
            VimUser user = vimUserService.selectVimUserById(userId);
            if (user != null) {
                return AjaxResult.success("用户查询成功", user);
            } else {
                return AjaxResult.error("用户不存在");
            }
        } catch (Exception e) {
            return AjaxResult.error("用户查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询用户的下级玩家列表
     */
    @ApiOperation(value = "查询下级玩家", notes = "查询指定用户的下级玩家明细（通过邀请码绑定关系）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path", example = "1"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", paramType = "query", example = "1"),
            @ApiImplicitParam(name = "pageSize", value = "每页数量", dataType = "Integer", paramType = "query", example = "10")
    })
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:query')")
    @GetMapping("/{userId}/subordinates")
    public TableDataInfo getSubordinateUsers(@PathVariable("userId") Long userId,
                                           @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                           @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                TableDataInfo dataTable = new TableDataInfo();
                dataTable.setCode(400);
                dataTable.setMsg("用户ID不能为空且必须大于0");
                return dataTable;
            }

            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 10;
            }
            if (pageSize > 100) {
                pageSize = 100; // 限制最大页面大小
            }

            logger.info("查询下级用户列表 - 用户ID: {}, 页码: {}, 每页大小: {}", userId, pageNum, pageSize);

            // 使用手动分页方法
            com.ruoyi.common.utils.PageResult<SubordinateUserVO> result = vimUserService.selectSubordinateUsersWithPagination(userId, pageNum, pageSize);

            // 转换为TableDataInfo格式
            TableDataInfo dataTable = result.toTableDataInfo("查询成功，共找到 " + result.getTotal() + " 个下级用户");

            logger.info("查询下级用户列表完成 - 总数: {}, 当前页数据: {}", result.getTotal(), result.getCurrentPageSize());
            return dataTable;
        } catch (Exception e) {
            logger.error("查询下级用户失败，用户ID: {}, 错误信息: {}", userId, e.getMessage(), e);
            TableDataInfo dataTable = new TableDataInfo();
            dataTable.setCode(500);
            dataTable.setMsg("查询下级用户失败：" + e.getMessage());
            return dataTable;
        }
    }

    /**
     * 统计用户的下级玩家数量
     */
    @ApiOperation(value = "统计下级玩家数量", notes = "统计指定用户的下级玩家数量")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:query')")
    @GetMapping("/{userId}/subordinates/count")
    public AjaxResult countSubordinateUsers(@PathVariable("userId") Long userId) {
        try {
            int count = vimUserService.countSubordinateUsers(userId);
            return AjaxResult.success("统计成功", count);
        } catch (Exception e) {
            return AjaxResult.error("统计下级用户数量失败：" + e.getMessage());
        }
    }

    /**
     * 重置盲盒系统用户密码
     */
    @GetMapping("resetPassword/{id}")
    @ApiOperation(value = "重置盲盒系统用户密码", notes = "重置盲盒系统用户密码")
    @ApiImplicitParam(name = "vimUserId", value = "盲盒用户ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @PreAuthorize("@ss.hasPermi('VimUserSys:VimUsers:edit')")
    @Log(title = "盲盒用户", businessType = BusinessType.UPDATE)
    public AjaxResult resetPassword(@PathVariable("id") Long vimUserId) {
        // 修正返回逻辑：成功时返回success，失败时返回error
        return vimUserService.resetPassword(vimUserId) ? AjaxResult.success("重置密码成功") : AjaxResult.error("重置密码失败");
    }

}
