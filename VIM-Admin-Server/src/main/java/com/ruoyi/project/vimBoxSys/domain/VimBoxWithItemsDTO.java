package com.ruoyi.project.vimBoxSys.domain;

import com.ruoyi.project.commoditySys.domain.VimItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 盲盒及其商品信息DTO
 * 用于批量查询优化，减少N+1查询问题
 * 
 * <AUTHOR> and 羊
 * @date 2025-06-28
 */
@ApiModel(value = "VimBoxWithItemsDTO", description = "盲盒及其商品信息组合对象")
public class VimBoxWithItemsDTO {
    
    /** 盲盒ID */
    @ApiModelProperty(value = "盲盒ID", example = "1")
    private Long boxId;
    
    /** 盲盒名称 */
    @ApiModelProperty(value = "盲盒名称", example = "神秘盲盒")
    private String boxName;
    
    /** 盲盒价格 */
    @ApiModelProperty(value = "盲盒价格", example = "99.99")
    private BigDecimal boxPrice;
    
    /** 商品ID */
    @ApiModelProperty(value = "商品ID", example = "101")
    private Long itemId;
    
    /** 商品名称 */
    @ApiModelProperty(value = "商品名称", example = "稀有道具")
    private String itemName;
    
    /** 商品展示价格 */
    @ApiModelProperty(value = "商品展示价格", example = "199.99")
    private BigDecimal itemPriceShow;

    /** 商品回收价格 */
    @ApiModelProperty(value = "商品回收价格", example = "79.99", notes = "用于返奖率计算的回收价格")
    private BigDecimal itemPriceRecycle;

    /** 概率权重 */
    @ApiModelProperty(value = "概率权重", example = "100", notes = "权重越高，抽到的概率越大")
    private Long probability;
    
    /** 概率等级 */
    @ApiModelProperty(value = "概率等级", example = "3", notes = "物品稀有程度，数字越大越稀有")
    private Long level;

    // Getters and Setters
    public Long getBoxId() {
        return boxId;
    }

    public void setBoxId(Long boxId) {
        this.boxId = boxId;
    }

    public String getBoxName() {
        return boxName;
    }

    public void setBoxName(String boxName) {
        this.boxName = boxName;
    }

    public BigDecimal getBoxPrice() {
        return boxPrice;
    }

    public void setBoxPrice(BigDecimal boxPrice) {
        this.boxPrice = boxPrice;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public BigDecimal getItemPriceShow() {
        return itemPriceShow;
    }

    public void setItemPriceShow(BigDecimal itemPriceShow) {
        this.itemPriceShow = itemPriceShow;
    }

    public BigDecimal getItemPriceRecycle() {
        return itemPriceRecycle;
    }

    public void setItemPriceRecycle(BigDecimal itemPriceRecycle) {
        this.itemPriceRecycle = itemPriceRecycle;
    }

    public Long getProbability() {
        return probability;
    }

    public void setProbability(Long probability) {
        this.probability = probability;
    }

    public Long getLevel() {
        return level;
    }

    public void setLevel(Long level) {
        this.level = level;
    }

    @Override
    public String toString() {
        return "VimBoxWithItemsDTO{" +
                "boxId=" + boxId +
                ", boxName='" + boxName + '\'' +
                ", boxPrice=" + boxPrice +
                ", itemId=" + itemId +
                ", itemName='" + itemName + '\'' +
                ", itemPriceShow=" + itemPriceShow +
                ", itemPriceRecycle=" + itemPriceRecycle +
                ", probability=" + probability +
                ", level=" + level +
                '}';
    }
}
