package com.ruoyi.project.vimRoll.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.vimRoll.domain.VimNewRoll;
import com.ruoyi.project.vimRoll.domain.result.BatchOperationResult;
import com.ruoyi.project.vimRoll.domain.result.TaskCreationResult;
import com.ruoyi.project.vimRoll.domain.vo.VimNewRollVO;
import com.ruoyi.project.vimRoll.service.IVimNewRollService;
import com.ruoyi.project.vimRoll.service.IRollDrawScheduleService;
import com.ruoyi.project.vimRoll.service.ITaskScheduleManager;
import org.quartz.SchedulerException;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.beans.factory.annotation.Value;

/**
 * Roll房活动Controller
 * 
 * <AUTHOR>
 * @date 2024-06-30
 */
@Api(tags = "Roll房活动管理")
@RestController
@RequestMapping("/vimRoll/roll")
public class VimNewRollController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(VimNewRollController.class);

    @Autowired
    private IVimNewRollService vimNewRollService;

    @Autowired
    private IRollDrawScheduleService rollDrawScheduleService;

    @Autowired
    private ITaskScheduleManager taskScheduleManager;

    @Autowired
    private RestTemplate restTemplate;

    // 外部接口配置
    @Value("${vim.roll.external.dev-url:http://192.168.64.132:5190/api}")
    private String devExternalUrl;

    @Value("${vim.roll.external.prod-url:https://www.voltskins.top/api}")
    private String prodExternalUrl;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    /**
     * 查询Roll房活动列表
     */
    @ApiOperation(value = "查询Roll房列表", notes = "获取Roll房分页列表数据")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:list')")
    @GetMapping("/list")
    public TableDataInfo list(VimNewRoll vimNewRoll)
    {
        startPage();
        List<VimNewRoll> list = vimNewRollService.selectVimNewRollList(vimNewRoll);
        return getDataTable(list);
    }

    /**
     * 导出Roll房活动列表
     */
    @ApiOperation(value = "导出Roll房列表", notes = "导出Roll房数据到Excel文件")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:export')")
    @Log(title = "Roll房活动", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VimNewRoll vimNewRoll)
    {
        List<VimNewRoll> list = vimNewRollService.selectVimNewRollList(vimNewRoll);
        ExcelUtil<VimNewRoll> util = new ExcelUtil<VimNewRoll>(VimNewRoll.class);
        util.exportExcel(response, list, "Roll房活动数据");
    }

    /**
     * 获取Roll房活动详细信息
     */
    @ApiOperation(value = "获取Roll房详情", notes = "根据ID获取Roll房详细信息")
    @ApiImplicitParam(name = "id", value = "Roll房ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(vimNewRollService.selectVimNewRollDetailById(id));
    }

    /**
     * 新增Roll房活动
     */
    @ApiOperation(value = "新增Roll房", notes = "新增Roll房信息")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:add')")
    @Log(title = "Roll房活动", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VimNewRollVO vimNewRollVO)
    {
        logger.info("📝 [API调用] 新增Roll房请求，标题: {}, 用户: {}", 
            vimNewRollVO.getTitle(), getUsername());
        
        try {
            // 参数验证
            if (vimNewRollVO == null) {
                logger.warn("❌ [参数错误] Roll房数据为空");
                return error("Roll房数据不能为空");
            }
            
            // 调用服务层创建Roll房
            int result = vimNewRollService.insertVimNewRoll(vimNewRollVO);
            
            if (result > 0) {
                // 创建成功，返回详细信息
                Map<String, Object> data = new HashMap<>();
                data.put("rollId", vimNewRollVO.getId());
                data.put("title", vimNewRollVO.getTitle());
                data.put("status", vimNewRollVO.getStatus());
                
                // 添加创建统计信息
                if (vimNewRollVO.getItems() != null) {
                    data.put("itemCount", vimNewRollVO.getItems().size());
                }
                if (vimNewRollVO.getRulers() != null) {
                    data.put("rulerCount", vimNewRollVO.getRulers().size());
                }
                
                logger.info("✅ [创建成功] Roll房创建成功，ID: {}, 标题: {}", 
                    vimNewRollVO.getId(), vimNewRollVO.getTitle());
                
                return AjaxResult.success("Roll房创建成功", data);
            } else {
                logger.error("❌ [创建失败] Roll房创建失败，标题: {}", vimNewRollVO.getTitle());
                return error("Roll房创建失败，请检查数据完整性");
            }
            
        } catch (IllegalArgumentException e) {
            logger.warn("❌ [参数错误] Roll房创建参数错误，标题: {}, 错误: {}", 
                vimNewRollVO.getTitle(), e.getMessage());
            return error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            logger.error("💥 [创建异常] Roll房创建异常，标题: {}, 异常: {}", 
                vimNewRollVO.getTitle(), e.getMessage(), e);
            return error("Roll房创建失败: " + e.getMessage());
        }
    }

    /**
     * 修改Roll房活动
     */
    @ApiOperation(value = "修改Roll房", notes = "修改Roll房信息")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:edit')")
    @Log(title = "Roll房活动", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VimNewRollVO vimNewRollVO)
    {
        return toAjax(vimNewRollService.updateVimNewRoll(vimNewRollVO));
    }

    /**
     * 删除Roll房活动
     */
    @ApiOperation(value = "删除Roll房", notes = "根据ID数组批量删除Roll房")
    @ApiImplicitParam(name = "ids", value = "Roll房ID数组", required = true, dataType = "Long[]", paramType = "path", example = "1,2,3")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:remove')")
    @Log(title = "Roll房活动", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        logger.info("🗑️ [API调用] 批量删除Roll房请求，数量: {}, 用户: {}", 
            ids != null ? ids.length : 0, getUsername());
        
        try {
            // 参数验证
            if (ids == null || ids.length == 0) {
                logger.warn("❌ [参数错误] Roll房ID数组为空");
                return error("请选择要删除的Roll房");
            }
            
            // 统计信息
            int totalCount = ids.length;
            int successCount = 0;
            int taskCancelSuccessCount = 0;
            int taskCancelFailureCount = 0;
            List<String> failedTasks = new ArrayList<>();
            
            // 批量取消定时任务
            logger.info("🔄 [任务取消] 开始批量取消定时任务，数量: {}", totalCount);
            
            List<Long> rollIdList = Arrays.asList(ids);
            BatchOperationResult<TaskCreationResult> taskResult = taskScheduleManager.cancelRollTasks(rollIdList);
            
            // 统计任务取消结果
            for (TaskCreationResult result : taskResult.getResults()) {
                if (result.isSuccess() || "TASK_NOT_FOUND".equals(result.getErrorCode())) {
                    taskCancelSuccessCount++;
                } else {
                    taskCancelFailureCount++;
                    failedTasks.add(result.getTaskId() + ": " + result.getMessage());
                }
            }
            
            logger.info("📊 [任务取消统计] 成功: {}, 失败: {}, 总数: {}", 
                taskCancelSuccessCount, taskCancelFailureCount, totalCount);
            
            // 记录任务取消失败的详细信息
            if (!failedTasks.isEmpty()) {
                logger.warn("⚠️ [任务取消警告] 部分定时任务取消失败: {}", failedTasks);
            }
            
            // 执行数据库删除操作
            int deleteResult = vimNewRollService.deleteVimNewRollByIds(ids);
            
            if (deleteResult > 0) {
                successCount = deleteResult;
                
                // 构建返回数据
                Map<String, Object> data = new HashMap<>();
                data.put("totalCount", totalCount);
                data.put("successCount", successCount);
                data.put("taskCancelSuccessCount", taskCancelSuccessCount);
                data.put("taskCancelFailureCount", taskCancelFailureCount);
                
                if (!failedTasks.isEmpty()) {
                    data.put("taskCancelFailures", failedTasks);
                }
                
                String message = String.format("成功删除 %d 个Roll房", successCount);
                if (taskCancelFailureCount > 0) {
                    message += String.format("，其中 %d 个定时任务取消失败", taskCancelFailureCount);
                }
                
                logger.info("✅ [删除成功] {}", message);
                return AjaxResult.success(message, data);
            } else {
                logger.error("❌ [删除失败] Roll房删除失败");
                return error("Roll房删除失败");
            }
            
        } catch (Exception e) {
            logger.error("💥 [删除异常] 批量删除Roll房异常，异常: {}", e.getMessage(), e);
            return error("删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行Roll房开奖
     * 通过后端转发调用外部开奖接口
     */
    @ApiOperation(value = "执行Roll房开奖", notes = "根据ID执行Roll房开奖操作，调用外部开奖接口")
    @ApiImplicitParam(name = "id", value = "Roll房ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:draw')")
    @Log(title = "Roll房开奖", businessType = BusinessType.UPDATE)
    @PostMapping("/draw/{id}")
    public AjaxResult draw(@PathVariable("id") Long id)
    {
        try {
            // 检查Roll房是否存在
            VimNewRoll roll = vimNewRollService.selectVimNewRollById(id);
            if (roll == null) {
                return error("Roll房不存在");
            }

            // 检查Roll房状态是否为待开奖状态(1)
            if (roll.getStatus() != 1) {
                return error("只有待开奖状态的Roll房才能执行开奖");
            }

            // 调用外部开奖接口
            String externalUrl = getExternalApiUrl();
            String openRollUrl = externalUrl + "/roll/OpenRollRoom";

            // 准备请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("id", id);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestData, headers);

            logger.info("调用外部开奖接口: {}, Roll房ID: {}", openRollUrl, id);

            // 发送请求
            ResponseEntity<String> response = restTemplate.postForEntity(openRollUrl, requestEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("外部开奖接口调用成功，Roll房ID: {}, 响应: {}", id, response.getBody());
                return success("开奖成功");
            } else {
                logger.error("外部开奖接口调用失败，Roll房ID: {}, 状态码: {}, 响应: {}", id, response.getStatusCode(), response.getBody());
                return error("开奖失败，外部接口返回错误");
            }

        } catch (Exception e) {
            logger.error("执行Roll房开奖失败，Roll房ID: {}", id, e);
            return error("开奖失败: " + e.getMessage());
        }
    }

    /**
     * 根据环境获取外部API地址
     */
    private String getExternalApiUrl() {
        if ("dev".equals(activeProfile)) {
            return devExternalUrl;
        } else {
            return prodExternalUrl;
        }
    }

    /**
     * 获取Roll房奖品列表
     */
    @ApiOperation(value = "获取Roll房奖品列表", notes = "根据Roll房ID获取奖品列表")
    @ApiImplicitParam(name = "id", value = "Roll房ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:query')")
    @GetMapping("/{id}/items")
    public AjaxResult getItems(@PathVariable("id") Long id)
    {
        return success(vimNewRollService.selectVimNewRollItemsByRollId(id));
    }

    /**
     * 获取Roll房参与用户列表
     */
    @ApiOperation(value = "获取Roll房参与用户列表", notes = "根据Roll房ID获取参与用户列表")
    @ApiImplicitParam(name = "id", value = "Roll房ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:query')")
    @GetMapping("/{id}/users")
    public AjaxResult getUsers(@PathVariable("id") Long id)
    {
        return success(vimNewRollService.selectVimNewRollUsersByRollId(id));
    }

    /**
     * 获取Roll房参与条件列表
     */
    @ApiOperation(value = "获取Roll房参与条件列表", notes = "根据Roll房ID获取参与条件列表")
    @ApiImplicitParam(name = "id", value = "Roll房ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:query')")
    @GetMapping("/{id}/rulers")
    public AjaxResult getRulers(@PathVariable("id") Long id)
    {
        return success(vimNewRollService.selectVimNewRollRulersByRollId(id));
    }

    /**
     * 获取Roll房中奖用户列表
     */
    @ApiOperation(value = "获取Roll房中奖用户列表", notes = "根据Roll房ID获取中奖用户列表")
    @ApiImplicitParam(name = "id", value = "Roll房ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:query')")
    @GetMapping("/{id}/winners")
    public AjaxResult getWinners(@PathVariable("id") Long id)
    {
        return success(vimNewRollService.selectVimNewRollWinnersByRollId(id));
    }

    /**
     * 开放Roll房
     */
    @ApiOperation(value = "开放Roll房", notes = "将Roll房状态从未开放改为未开奖")
    @ApiImplicitParam(name = "id", value = "Roll房ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @PreAuthorize("@ss.hasPermi('vimRoll:roll:edit')")
    @Log(title = "Roll房开放", businessType = BusinessType.UPDATE)
    @PostMapping("/open/{id}")
    public AjaxResult openRoll(@PathVariable("id") Long id)
    {
        logger.info("🔓 [API调用] 开放Roll房请求，Roll房ID: {}, 用户: {}", id, getUsername());
        
        try {
            // 参数验证
            if (id == null || id <= 0) {
                logger.warn("❌ [参数错误] Roll房ID无效: {}", id);
                return error("Roll房ID无效");
            }
            
            // 调用服务层开放Roll房
            int result = vimNewRollService.openRoll(id);
            
            if (result > 0) {
                // 开放成功，获取Roll房信息用于返回
                VimNewRoll roll = vimNewRollService.selectVimNewRollById(id);
                
                Map<String, Object> data = new HashMap<>();
                data.put("rollId", id);
                if (roll != null) {
                    data.put("title", roll.getTitle());
                    data.put("status", roll.getStatus());
                    data.put("endTime", roll.getEndTime());
                }
                
                logger.info("✅ [开放成功] Roll房开放成功，ID: {}, 标题: {}", 
                    id, roll != null ? roll.getTitle() : "未知");
                
                return AjaxResult.success("Roll房开放成功", data);
            } else {
                logger.warn("❌ [开放失败] Roll房开放失败，Roll房ID: {}", id);
                return error("Roll房开放失败，请检查Roll房状态（只有未开放状态的Roll房才能开放）");
            }
            
        } catch (IllegalArgumentException e) {
            logger.warn("❌ [参数错误] Roll房开放参数错误，Roll房ID: {}, 错误: {}", id, e.getMessage());
            return error("参数错误: " + e.getMessage());
        } catch (Exception e) {
            logger.error("💥 [开放异常] Roll房开放异常，Roll房ID: {}, 异常: {}", id, e.getMessage(), e);
            return error("开放失败: " + e.getMessage());
        }
    }
}