package com.ruoyi.project.vimRoll.enums;

/**
 * Roll房参与条件类型枚举
 * 定义Roll房活动的参与条件类型
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public enum RollConditionType {
    
    /**
     * 零门槛 - 所有用户都可以参与
     */
    NO_THRESHOLD(0, "零门槛", "所有用户都可以参与", false),
    
    /**
     * 注册日期 - 根据用户注册时间限制
     */
    REGISTER_DATE(1, "注册日期", "根据用户注册时间限制参与", false),
    
    /**
     * 日充值金额 - 根据用户当日充值金额限制
     */
    DAILY_RECHARGE(2, "日充值金额", "根据用户当日充值金额限制参与", false),
    
    /**
     * 周充值金额 - 根据用户本周充值金额限制
     */
    WEEKLY_RECHARGE(3, "周充值金额", "根据用户本周充值金额限制参与", false),
    
    /**
     * 月充值金额 - 根据用户本月充值金额限制
     */
    MONTHLY_RECHARGE(4, "月充值金额", "根据用户本月充值金额限制参与", false),
    
    /**
     * 专属Roll房 - 绑定特定上级用户，只有该用户的下级用户可以参与
     */
    EXCLUSIVE_ROLL(5, "专属Roll房（上级用户ID）", "绑定特定上级用户，只有该用户的下级用户可以参与", true),
    
    /**
     * 私密房间 - 需要特殊权限或邀请才能参与
     */
    PRIVATE_ROOM(6, "私密房间", "需要特殊权限或邀请才能参与", false);
    
    private final Integer code;
    private final String name;
    private final String description;
    private final boolean needsUserSelection;
    
    RollConditionType(Integer code, String name, String description, boolean needsUserSelection) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.needsUserSelection = needsUserSelection;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public boolean isNeedsUserSelection() {
        return needsUserSelection;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static RollConditionType fromCode(Integer code) {
        if (code == null) {
            return NO_THRESHOLD;
        }
        
        for (RollConditionType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return NO_THRESHOLD;
    }
    
    /**
     * 获取所有条件类型的选项列表（用于前端）
     */
    public static RollConditionType[] getAllTypes() {
        return values();
    }
    
    /**
     * 检查是否为有效的条件类型代码
     */
    public static boolean isValidCode(Integer code) {
        if (code == null) {
            return false;
        }
        
        for (RollConditionType type : values()) {
            if (type.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
