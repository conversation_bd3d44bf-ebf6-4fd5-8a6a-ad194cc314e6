package com.ruoyi.project.commoditySys.service;

import java.util.List;
import com.ruoyi.project.commoditySys.domain.VimItem;

/**
 * 商品管理Service接口
 * 
 * <AUTHOR> and 羊
 * @date 2025-03-13
 */
public interface IVimItemService 
{
    /**
     * 查询商品管理
     * 
     * @param id 商品管理主键
     * @return 商品管理
     */
    public VimItem selectVimItemById(Long id);

    /**
     * 查询商品管理列表
     * 
     * @param vimItem 商品管理
     * @return 商品管理集合
     */
    public List<VimItem> selectVimItemList(VimItem vimItem);

    /**
     * 新增商品管理
     * 
     * @param vimItem 商品管理
     * @return 结果
     */
    public int insertVimItem(VimItem vimItem);

    /**
     * 修改商品管理
     * 
     * @param vimItem 商品管理
     * @return 结果
     */
    public int updateVimItem(VimItem vimItem);

    /**
     * 批量删除商品管理
     * 
     * @param ids 需要删除的商品管理主键集合
     * @return 结果
     */
    public int deleteVimItemByIds(List<Long> ids);

    /**
     * 删除商品管理信息
     * 
     * @param id 商品管理主键
     * @return 结果
     */
    public int deleteVimItemById(Long id);


    /**
     * 查询所有的商品列表
     */
    public List<VimItem> selectAllVimItemList();

    /**
     * 根据name查询商品
     */
    List<VimItem> selectVimItemByItemName(String name);

}
