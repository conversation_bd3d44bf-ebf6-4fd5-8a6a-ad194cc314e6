package com.ruoyi.project.VimOrderClaimSys.controller;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.VimOrderBoxSys.domain.VimOrderBox;
import com.ruoyi.project.VimOrderBoxSys.service.IVimOrderBoxService;
import com.ruoyi.project.VimOrderClaimSys.domain.VimOrderClaim;
import com.ruoyi.project.VimOrderClaimSys.domain.vo.VimOrderClaimVO;
import com.ruoyi.project.VimOrderClaimSys.service.IVimOrderClaimService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

import static com.ruoyi.framework.datasource.DynamicDataSourceContextHolder.log;

/**
 * 订单发货Controller
 * 
 * <AUTHOR> and 羊
 * @date 2025-04-16
 */
@Api(tags = "订单发货管理", description = "提货订单的处理和发货操作")
@RestController
@RequestMapping("/VimOrderClaimSys/VimOrderClaims")
public class VimOrderClaimController extends BaseController
{
    @Autowired
    private IVimOrderClaimService vimOrderClaimService;

    @Autowired
    private IVimOrderBoxService vimOrderBoxService;

    /**
     * 查询订单发货列表（包含用户信息）
     */
    @ApiOperation(value = "查询发货订单列表", notes = "获取订单发货分页列表数据，包含用户昵称和手机号")
    @PreAuthorize("@ss.hasPermi('VimOrderClaimSys:VimOrderClaims:list')")
    @GetMapping("/list")
    public TableDataInfo list(VimOrderClaim vimOrderClaim, String orderBy)
    {
        startPage();

        // 设置排序参数
        if (StringUtils.isNotEmpty(orderBy)) {
            vimOrderClaim.getParams().put("orderBy", orderBy);
        }

        List<VimOrderClaimVO> list = vimOrderClaimService.selectVimOrderClaimList(vimOrderClaim);
        return getDataTable(list);
    }

    /**
     * 导出订单发货列表（包含用户信息）
     */
    @ApiOperation(value = "导出发货订单列表", notes = "导出订单发货数据到Excel文件，包含用户信息")
    @PreAuthorize("@ss.hasPermi('VimOrderClaimSys:VimOrderClaims:export')")
    @Log(title = "订单发货", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VimOrderClaim vimOrderClaim)
    {
        List<VimOrderClaimVO> list = vimOrderClaimService.selectVimOrderClaimList(vimOrderClaim);
        ExcelUtil<VimOrderClaimVO> util = new ExcelUtil<VimOrderClaimVO>(VimOrderClaimVO.class);
        util.exportExcel(response, list, "订单发货数据");
    }

    /**
     * 获取订单发货详细信息
     */
    @ApiOperation(value = "获取发货订单详情", notes = "根据ID获取订单发货详细信息")
    @ApiImplicitParam(name = "id", value = "提货订单ID", required = true, dataType = "String", paramType = "path", example = "ORDER123456")
    @PreAuthorize("@ss.hasPermi('VimOrderClaimSys:VimOrderClaims:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(vimOrderClaimService.selectVimOrderClaimById(id));
    }

    /**
     * 新增订单发货
     */
    @ApiOperation(value = "新增发货订单", notes = "新增订单发货信息")
    @PreAuthorize("@ss.hasPermi('VimOrderClaimSys:VimOrderClaims:add')")
    @Log(title = "订单发货", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VimOrderClaim vimOrderClaim)
    {
        try {
            return toAjax(vimOrderClaimService.insertVimOrderClaim(vimOrderClaim));
        } catch (Exception e) {
            log.error("新增订单发货失败", e);
            return AjaxResult.error("新增订单发货失败");
        }
    }

    /**
     * 修改订单发货
     */
    @ApiOperation(value = "修改发货订单", notes = "修改订单发货信息")
    @PreAuthorize("@ss.hasPermi('VimOrderClaimSys:VimOrderClaims:edit')")
    @Log(title = "订单发货", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VimOrderClaim vimOrderClaim)
    {
        System.out.println("接收数据-------------"+vimOrderClaim);

        // 获取当前订单信息
        VimOrderClaim currentOrder = vimOrderClaimService.selectVimOrderClaimById(vimOrderClaim.getId());
        if (currentOrder == null) {
            return AjaxResult.error("订单不存在");
        }

        // 判断是否为仅修改发货价格操作
        boolean isOnlyUpdateCost = isOnlyUpdateCostOperation(vimOrderClaim);

        if (isOnlyUpdateCost) {
            // 仅修改发货价格操作，不需要检查订单状态
            log.info("修改订单发货价格，订单ID: {}, 新价格: {}", vimOrderClaim.getId(), vimOrderClaim.getCost());
            return toAjax(vimOrderClaimService.updateVimOrderClaim(vimOrderClaim));
        }

        // 判断是否为锁价操作
        boolean isLockPriceOperation = isLockPriceOperation(vimOrderClaim);
        
        if (isLockPriceOperation) {
            // 锁价操作：只能对状态为1（发货中）的订单进行锁价
            if (currentOrder.getState() != null && currentOrder.getState() != 1L) {
                return AjaxResult.error("只能对发货中的订单进行锁价操作");
            }
            
            log.info("锁价操作，订单ID: {}", vimOrderClaim.getId());
            
            // 更新订单状态为4（已锁价未发货）
            VimOrderClaim updateClaim = new VimOrderClaim();
            updateClaim.setId(vimOrderClaim.getId());
            updateClaim.setState(4L);
            updateClaim.setInfo("已锁价未发货");
            
            return toAjax(vimOrderClaimService.updateVimOrderClaim(updateClaim));
        }

        // 实际发货操作：执行重复发货防护检查
        if (currentOrder.getState() != null && currentOrder.getState() == 2L) {
            return AjaxResult.error("该订单已发货，无法重复发货");
        }
        
        // 如果订单已锁价，不能直接发货
        if (currentOrder.getState() != null && currentOrder.getState() == 4L) {
            return AjaxResult.error("该订单已锁价，无法直接发货");
        }

        // 如果订单发货失败且不是失败处理操作，阻止操作
        if (currentOrder.getState() != null && currentOrder.getState() == 3L &&
            (vimOrderClaim.getInfo() == null || vimOrderClaim.getInfo().isEmpty())) {
            return AjaxResult.error("该订单发货失败，请先处理失败状态");
        }

        String oid = vimOrderClaim.getOid();
        VimOrderBox vimOrderBox = new VimOrderBox();
        vimOrderBox.setOid(oid);
        if (vimOrderClaim.getInfo() == null || vimOrderClaim.getInfo().isEmpty()) {
            System.out.println("发货成功-------------"+vimOrderClaim.getInfo());
            vimOrderClaim.setInfo("发货成功");
            vimOrderBox.setState(4L);
            vimOrderBoxService.updateVimOrderBox(vimOrderBox);
            return toAjax(vimOrderClaimService.updateVimOrderClaim(vimOrderClaim));
        }else {
            log.info("发货失败处理，原因: {}", vimOrderClaim.getInfo());
            vimOrderBox.setState(5L);
            System.out.println("发货失败-------------"+vimOrderBox.getState());
            System.out.println("发货失败-------------"+vimOrderBox.getOid());
            vimOrderBoxService.updateVimOrderBox(vimOrderBox);
            // 只修改前端传递过来vim_order_claim对应的id字段中info字段和state字段设为3
            VimOrderClaim updateClaim = new VimOrderClaim();
            updateClaim.setId(vimOrderClaim.getId());
            updateClaim.setInfo(vimOrderClaim.getInfo());
            updateClaim.setState(3L);
            vimOrderBox.setState(3L);
            vimOrderBoxService.updateVimOrderBox(vimOrderBox);
            System.out.println("发货失败-------------"+updateClaim);
            return toAjax(vimOrderClaimService.updateVimOrderClaim(updateClaim));
        }
    }

    /**
     * 删除订单发货
     */
    @ApiOperation(value = "删除发货订单", notes = "根据ID数组批量删除订单发货")
    @ApiImplicitParam(name = "ids", value = "提货订单ID数组", required = true, dataType = "String[]", paramType = "path", example = "ORDER123456,ORDER123457")
    @PreAuthorize("@ss.hasPermi('VimOrderClaimSys:VimOrderClaims:remove')")
    @Log(title = "订单发货", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(vimOrderClaimService.deleteVimOrderClaimByIds(ids));
    }

    /**
     * 判断是否为仅修改发货价格操作
     *
     * @param vimOrderClaim 订单发货对象
     * @return true-仅修改发货价格，false-其他操作
     */
    private boolean isOnlyUpdateCostOperation(VimOrderClaim vimOrderClaim) {
        // 检查是否只有id和cost字段有值，其他业务字段都为空
        return vimOrderClaim.getCost() != null &&
               vimOrderClaim.getOid() == null &&
               vimOrderClaim.getUid() == null &&
               vimOrderClaim.getItemid() == null &&
               vimOrderClaim.getItemname() == null &&
               vimOrderClaim.getHashname() == null &&
               vimOrderClaim.getCreatTime() == null &&
               vimOrderClaim.getClaimTime() == null &&
               vimOrderClaim.getState() == null &&
               vimOrderClaim.getSteamid() == null &&
               vimOrderClaim.getSteamlink() == null &&
               (vimOrderClaim.getInfo() == null || vimOrderClaim.getInfo().isEmpty());
    }
    
    /**
     * 判断是否为锁价操作
     *
     * @param vimOrderClaim 订单发货对象
     * @return true-锁价操作，false-其他操作
     */
    private boolean isLockPriceOperation(VimOrderClaim vimOrderClaim) {
        // 检查是否只有id和state字段有值，且state为4（已锁价未发货）
        return vimOrderClaim.getState() != null &&
               vimOrderClaim.getState() == 4L &&
               vimOrderClaim.getCost() == null &&
               vimOrderClaim.getOid() == null &&
               vimOrderClaim.getUid() == null &&
               vimOrderClaim.getItemid() == null &&
               vimOrderClaim.getItemname() == null &&
               vimOrderClaim.getHashname() == null &&
               vimOrderClaim.getCreatTime() == null &&
               vimOrderClaim.getClaimTime() == null &&
               vimOrderClaim.getSteamid() == null &&
               vimOrderClaim.getSteamlink() == null &&
               (vimOrderClaim.getInfo() == null || vimOrderClaim.getInfo().isEmpty());
    }
}
