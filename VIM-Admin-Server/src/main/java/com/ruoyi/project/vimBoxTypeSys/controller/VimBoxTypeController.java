package com.ruoyi.project.vimBoxTypeSys.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.vimBoxTypeSys.domain.VimBoxType;
import com.ruoyi.project.vimBoxTypeSys.service.IVimBoxTypeService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiImplicitParam;

/**
 * 盲盒分类Controller
 * 
 * <AUTHOR> and 羊
 * @date 2025-03-15
 */
@Api(tags = "盲盒分类管理", description = "盲盒分类信息的增删改查操作")
@RestController
@RequestMapping("/vimBoxTypeSys/vimBoxTypes")
public class VimBoxTypeController extends BaseController
{
    @Autowired
    private IVimBoxTypeService vimBoxTypeService;

    /**
     * 查询盲盒分类列表
     */
    @ApiOperation(value = "查询分类列表", notes = "获取盲盒分类分页列表数据")
    @PreAuthorize("@ss.hasPermi('vimBoxTypeSys:vimBoxTypes:list')")
    @GetMapping("/list")
    public TableDataInfo list(VimBoxType vimBoxType)
    {
        startPage();
        List<VimBoxType> list = vimBoxTypeService.selectVimBoxTypeList(vimBoxType);
        return getDataTable(list);
    }

    /**
     * 导出盲盒分类列表
     */
    @ApiOperation(value = "导出分类列表", notes = "导出盲盒分类数据到Excel文件")
    @PreAuthorize("@ss.hasPermi('vimBoxTypeSys:vimBoxTypes:export')")
    @Log(title = "盲盒分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VimBoxType vimBoxType)
    {
        List<VimBoxType> list = vimBoxTypeService.selectVimBoxTypeList(vimBoxType);
        ExcelUtil<VimBoxType> util = new ExcelUtil<VimBoxType>(VimBoxType.class);
        util.exportExcel(response, list, "盲盒分类数据");
    }

    /**
     * 获取盲盒分类详细信息
     */
    @ApiOperation(value = "获取分类详情", notes = "根据分类ID获取详细信息")
    @ApiImplicitParam(name = "id", value = "分类ID", required = true, dataType = "Long", paramType = "path", example = "1")
    @PreAuthorize("@ss.hasPermi('vimBoxTypeSys:vimBoxTypes:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(vimBoxTypeService.selectVimBoxTypeById(id));
    }

    /**
     * 新增盲盒分类
     */
    @ApiOperation(value = "新增分类", notes = "新增盲盒分类信息")
    @PreAuthorize("@ss.hasPermi('vimBoxTypeSys:vimBoxTypes:add')")
    @Log(title = "盲盒分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VimBoxType vimBoxType)
    {
        return toAjax(vimBoxTypeService.insertVimBoxType(vimBoxType));
    }

    /**
     * 修改盲盒分类
     */
    @ApiOperation(value = "修改分类", notes = "修改盲盒分类信息")
    @PreAuthorize("@ss.hasPermi('vimBoxTypeSys:vimBoxTypes:edit')")
    @Log(title = "盲盒分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VimBoxType vimBoxType)
    {
        return toAjax(vimBoxTypeService.updateVimBoxType(vimBoxType));
    }

    /**
     * 删除盲盒分类
     */
    @ApiOperation(value = "删除分类", notes = "根据ID数组批量删除盲盒分类")
    @ApiImplicitParam(name = "ids", value = "分类ID数组", required = true, dataType = "Long[]", paramType = "path", example = "1,2,3")
    @PreAuthorize("@ss.hasPermi('vimBoxTypeSys:vimBoxTypes:remove')")
    @Log(title = "盲盒分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(vimBoxTypeService.deleteVimBoxTypeByIds(ids));
    }
}
