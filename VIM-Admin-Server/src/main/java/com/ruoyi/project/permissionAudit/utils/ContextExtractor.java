package com.ruoyi.project.permissionAudit.utils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 上下文信息提取器
 * 用于提取文件路径、行号、方法名、类名等上下文信息
 * 
 * <AUTHOR>
 */
public class ContextExtractor {
    
    // 类定义模式
    private static final Pattern CLASS_PATTERN = Pattern.compile(
        "(?:public\\s+|private\\s+|protected\\s+)?(?:abstract\\s+|final\\s+)?class\\s+(\\w+)",
        Pattern.MULTILINE
    );
    
    // 方法定义模式
    private static final Pattern METHOD_PATTERN = Pattern.compile(
        "(?:@\\w+(?:\\([^)]*\\))?\\s*)*(?:public|private|protected)?\\s*(?:static\\s+)?(?:final\\s+)?\\w+\\s+(\\w+)\\s*\\([^)]*\\)\\s*(?:throws\\s+[^{]+)?\\s*\\{",
        Pattern.MULTILINE | Pattern.DOTALL
    );
    
    // 注解模式
    private static final Pattern ANNOTATION_PATTERN = Pattern.compile(
        "@(\\w+)(?:\\([^)]*\\))?",
        Pattern.MULTILINE
    );
    
    // 包声明模式
    private static final Pattern PACKAGE_PATTERN = Pattern.compile(
        "package\\s+([\\w.]+)\\s*;",
        Pattern.MULTILINE
    );
    
    // 导入声明模式
    private static final Pattern IMPORT_PATTERN = Pattern.compile(
        "import\\s+(?:static\\s+)?([\\w.*]+)\\s*;",
        Pattern.MULTILINE
    );
    
    /**
     * 提取文件的完整上下文信息
     * 
     * @param file 文件对象
     * @param content 文件内容
     * @param basePath 基础路径
     * @return 文件上下文信息
     */
    public static FileContext extractFileContext(File file, String content, String basePath) {
        FileContext context = new FileContext();
        
        // 基本文件信息
        context.setFileName(file.getName());
        context.setAbsolutePath(file.getAbsolutePath());
        context.setRelativePath(FileScanner.getRelativePath(file, basePath));
        
        // 提取包名
        context.setPackageName(extractPackageName(content));
        
        // 提取类名
        context.setClassName(extractClassName(content));
        
        // 提取导入信息
        context.setImports(extractImports(content));
        
        // 提取方法信息
        context.setMethods(extractMethods(content));
        
        // 提取注解信息
        context.setAnnotations(extractAnnotations(content));
        
        return context;
    }
    
    /**
     * 根据行号提取上下文信息
     * 
     * @param content 文件内容
     * @param lineNumber 行号
     * @param contextLines 上下文行数
     * @return 上下文信息
     */
    public static LineContext extractLineContext(String content, int lineNumber, int contextLines) {
        LineContext context = new LineContext();
        context.setTargetLineNumber(lineNumber);
        
        String[] lines = content.split("\n");
        if (lineNumber <= 0 || lineNumber > lines.length) {
            return context;
        }
        
        // 计算上下文范围
        int startLine = Math.max(1, lineNumber - contextLines);
        int endLine = Math.min(lines.length, lineNumber + contextLines);
        
        context.setStartLine(startLine);
        context.setEndLine(endLine);
        
        // 提取上下文代码
        List<String> contextCode = new ArrayList<>();
        for (int i = startLine - 1; i < endLine; i++) {
            String prefix = (i + 1 == lineNumber) ? ">>> " : "    ";
            contextCode.add(String.format("%s%d: %s", prefix, i + 1, lines[i]));
        }
        context.setContextCode(contextCode);
        
        // 提取目标行内容
        context.setTargetLine(lines[lineNumber - 1]);
        
        // 查找所属方法
        context.setMethodName(findMethodAtLine(content, lineNumber));
        
        // 查找所属类
        context.setClassName(findClassAtLine(content, lineNumber));
        
        return context;
    }
    
    /**
     * 提取包名
     * 
     * @param content 文件内容
     * @return 包名
     */
    private static String extractPackageName(String content) {
        Matcher matcher = PACKAGE_PATTERN.matcher(content);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "";
    }
    
    /**
     * 提取类名
     * 
     * @param content 文件内容
     * @return 类名
     */
    private static String extractClassName(String content) {
        Matcher matcher = CLASS_PATTERN.matcher(content);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return "Unknown";
    }
    
    /**
     * 提取导入信息
     * 
     * @param content 文件内容
     * @return 导入列表
     */
    private static List<String> extractImports(String content) {
        List<String> imports = new ArrayList<>();
        Matcher matcher = IMPORT_PATTERN.matcher(content);
        
        while (matcher.find()) {
            imports.add(matcher.group(1));
        }
        
        return imports;
    }
    
    /**
     * 提取方法信息
     * 
     * @param content 文件内容
     * @return 方法信息列表
     */
    private static List<MethodInfo> extractMethods(String content) {
        List<MethodInfo> methods = new ArrayList<>();
        Matcher matcher = METHOD_PATTERN.matcher(content);
        
        while (matcher.find()) {
            MethodInfo method = new MethodInfo();
            method.setName(matcher.group(1));
            method.setStartPosition(matcher.start());
            method.setLineNumber(getLineNumber(content, matcher.start()));
            methods.add(method);
        }
        
        return methods;
    }
    
    /**
     * 提取注解信息
     * 
     * @param content 文件内容
     * @return 注解信息列表
     */
    private static List<AnnotationInfo> extractAnnotations(String content) {
        List<AnnotationInfo> annotations = new ArrayList<>();
        Matcher matcher = ANNOTATION_PATTERN.matcher(content);
        
        while (matcher.find()) {
            AnnotationInfo annotation = new AnnotationInfo();
            annotation.setName(matcher.group(1));
            annotation.setFullText(matcher.group(0));
            annotation.setLineNumber(getLineNumber(content, matcher.start()));
            annotations.add(annotation);
        }
        
        return annotations;
    }
    
    /**
     * 查找指定行号所属的方法
     * 
     * @param content 文件内容
     * @param lineNumber 行号
     * @return 方法名
     */
    private static String findMethodAtLine(String content, int lineNumber) {
        List<MethodInfo> methods = extractMethods(content);
        
        // 找到最接近且在目标行之前的方法
        MethodInfo targetMethod = null;
        for (MethodInfo method : methods) {
            if (method.getLineNumber() <= lineNumber) {
                if (targetMethod == null || method.getLineNumber() > targetMethod.getLineNumber()) {
                    targetMethod = method;
                }
            }
        }
        
        return targetMethod != null ? targetMethod.getName() : "Unknown";
    }
    
    /**
     * 查找指定行号所属的类
     * 
     * @param content 文件内容
     * @param lineNumber 行号
     * @return 类名
     */
    private static String findClassAtLine(String content, int lineNumber) {
        return extractClassName(content);
    }
    
    /**
     * 获取指定位置的行号
     * 
     * @param content 文件内容
     * @param position 字符位置
     * @return 行号
     */
    private static int getLineNumber(String content, int position) {
        if (position < 0 || position >= content.length()) {
            return 1;
        }
        
        int lineNumber = 1;
        for (int i = 0; i < position; i++) {
            if (content.charAt(i) == '\n') {
                lineNumber++;
            }
        }
        
        return lineNumber;
    }
    
    /**
     * 文件上下文信息
     */
    public static class FileContext {
        private String fileName;
        private String absolutePath;
        private String relativePath;
        private String packageName;
        private String className;
        private List<String> imports = new ArrayList<>();
        private List<MethodInfo> methods = new ArrayList<>();
        private List<AnnotationInfo> annotations = new ArrayList<>();
        
        // Getters and Setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public String getAbsolutePath() { return absolutePath; }
        public void setAbsolutePath(String absolutePath) { this.absolutePath = absolutePath; }
        
        public String getRelativePath() { return relativePath; }
        public void setRelativePath(String relativePath) { this.relativePath = relativePath; }
        
        public String getPackageName() { return packageName; }
        public void setPackageName(String packageName) { this.packageName = packageName; }
        
        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }
        
        public List<String> getImports() { return imports; }
        public void setImports(List<String> imports) { this.imports = imports; }
        
        public List<MethodInfo> getMethods() { return methods; }
        public void setMethods(List<MethodInfo> methods) { this.methods = methods; }
        
        public List<AnnotationInfo> getAnnotations() { return annotations; }
        public void setAnnotations(List<AnnotationInfo> annotations) { this.annotations = annotations; }
    }
    
    /**
     * 行上下文信息
     */
    public static class LineContext {
        private int targetLineNumber;
        private String targetLine;
        private int startLine;
        private int endLine;
        private List<String> contextCode = new ArrayList<>();
        private String methodName;
        private String className;
        
        // Getters and Setters
        public int getTargetLineNumber() { return targetLineNumber; }
        public void setTargetLineNumber(int targetLineNumber) { this.targetLineNumber = targetLineNumber; }
        
        public String getTargetLine() { return targetLine; }
        public void setTargetLine(String targetLine) { this.targetLine = targetLine; }
        
        public int getStartLine() { return startLine; }
        public void setStartLine(int startLine) { this.startLine = startLine; }
        
        public int getEndLine() { return endLine; }
        public void setEndLine(int endLine) { this.endLine = endLine; }
        
        public List<String> getContextCode() { return contextCode; }
        public void setContextCode(List<String> contextCode) { this.contextCode = contextCode; }
        
        public String getMethodName() { return methodName; }
        public void setMethodName(String methodName) { this.methodName = methodName; }
        
        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }
        
        public String getFormattedContext() {
            return String.join("\n", contextCode);
        }
    }
    
    /**
     * 方法信息
     */
    public static class MethodInfo {
        private String name;
        private int lineNumber;
        private int startPosition;
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public int getLineNumber() { return lineNumber; }
        public void setLineNumber(int lineNumber) { this.lineNumber = lineNumber; }
        
        public int getStartPosition() { return startPosition; }
        public void setStartPosition(int startPosition) { this.startPosition = startPosition; }
    }
    
    /**
     * 注解信息
     */
    public static class AnnotationInfo {
        private String name;
        private String fullText;
        private int lineNumber;
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getFullText() { return fullText; }
        public void setFullText(String fullText) { this.fullText = fullText; }
        
        public int getLineNumber() { return lineNumber; }
        public void setLineNumber(int lineNumber) { this.lineNumber = lineNumber; }
    }
}