package com.ruoyi.project.priceAlert.config;

import com.ruoyi.project.priceAlert.domain.VimPriceAlertConfig;
import com.ruoyi.project.priceAlert.mapper.VimPriceAlertConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 价格预警系统初始化器
 * 在应用启动时初始化默认配置
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class PriceAlertInitializer implements ApplicationRunner {

    @Autowired
    private VimPriceAlertConfigMapper configMapper;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始异步初始化价格预警系统配置...");

        // 异步初始化，避免阻塞启动流程
        new Thread(() -> {
            try {
                // 延迟2秒执行，确保数据库连接池已完全初始化
                Thread.sleep(2000);
                // 检查并初始化默认配置
                initializeDefaultConfigs();
                log.info("价格预警系统配置异步初始化完成");
            } catch (Exception e) {
                log.warn("价格预警系统配置异步初始化失败，可能是数据表不存在：{}", e.getMessage());
                // 不抛出异常，避免影响系统启动
            }
        }, "price-alert-init").start();
    }

    /**
     * 初始化默认配置
     */
    private void initializeDefaultConfigs() {
        // 检查是否已存在配置
        try {
            VimPriceAlertConfig upConfig = configMapper.selectConfigByRuleType("price_up");
            VimPriceAlertConfig downConfig = configMapper.selectConfigByRuleType("price_down");

            // 如果配置不存在，则创建默认配置
            if (upConfig == null) {
                createDefaultUpConfig();
                log.info("创建默认价格上涨预警配置");
            }

            if (downConfig == null) {
                createDefaultDownConfig();
                log.info("创建默认价格下跌预警配置");
            }
        } catch (Exception e) {
            log.warn("检查或创建默认配置失败：{}", e.getMessage());
        }
    }

    /**
     * 创建默认的价格上涨预警配置
     */
    private void createDefaultUpConfig() {
        VimPriceAlertConfig config = new VimPriceAlertConfig();
        config.setRuleType("price_up");
        config.setRuleName("全局商品价格上涨预警");
        // 百分比
        config.setThresholdType(1);
        config.setThresholdValue(new BigDecimal("15.0000"));
        config.setDataSources("YP");
        config.setDataSourcePriority("YP");
        config.setMinPriceFilter(new BigDecimal("50.00"));
        config.setExecutionTimes("09:00,15:00,21:00");
        config.setIsEnabled(1);
        config.setNotificationMethods("system");
        config.setCreateTime(String.valueOf(new Date()));
        config.setUpdateTime(String.valueOf(new Date()));
        config.setRemark("监控所有商品价格上涨超过阈值时触发预警");

        try {
            // 这里需要使用INSERT语句，因为表可能为空
            insertConfigDirectly(config);
        } catch (Exception e) {
            log.error("创建默认价格上涨配置失败：{}", e.getMessage());
        }
    }

    /**
     * 创建默认的价格下跌预警配置
     */
    private void createDefaultDownConfig() {
        VimPriceAlertConfig config = new VimPriceAlertConfig();
        config.setRuleType("price_down");
        config.setRuleName("全局商品价格下跌预警");
        // 百分比
        config.setThresholdType(1);
        config.setThresholdValue(new BigDecimal("10.0000"));
        config.setDataSources("YP");
        config.setDataSourcePriority("YP");
        config.setMinPriceFilter(new BigDecimal("20.00"));
        config.setExecutionTimes("09:00,15:00,21:00");
        config.setIsEnabled(1);
        config.setNotificationMethods("system");
        config.setCreateTime(String.valueOf(new Date()));
        config.setUpdateTime(String.valueOf(new Date()));
        config.setRemark("监控所有商品价格下跌超过阈值时触发预警");

        try {
            // 这里需要使用INSERT语句，因为表可能为空
            insertConfigDirectly(config);
        } catch (Exception e) {
            log.error("创建默认价格下跌配置失败：{}", e.getMessage());
        }
    }

    /**
     * 直接插入配置
     */
    private void insertConfigDirectly(VimPriceAlertConfig config) {
        try {
            // 先尝试插入
            int result = configMapper.insertConfig(config);
            if (result > 0) {
                log.info("成功插入配置：{}", config.getRuleType());
            }
        } catch (Exception e) {
            // 如果插入失败（可能是重复键），尝试更新
            try {
                int updateResult = configMapper.updateConfig(config);
                if (updateResult > 0) {
                    log.info("配置已存在，更新成功：{}", config.getRuleType());
                } else {
                    log.warn("配置插入和更新都失败：{}", config.getRuleType());
                }
            } catch (Exception updateEx) {
                log.error("配置更新失败：{}", updateEx.getMessage());
            }
        }
    }
}
